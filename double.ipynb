{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import torch\n", "import numpy as np\n", "import seaborn as sns\n", "import matplotlib.pyplot as plt\n", "from math import sin,pi\n", "\n", "# 检查是否有可用的 GPU\n", "device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["rt = 1.2\n", "d_one = 0.5\n", "d_two = 0.5\n", "l= 200\n", "z = 2 + 0*sin(2*pi/8000)\n", "ebsenr_one = 2.2\n", "ebsenr_two = 2.2\n", "ebr_one = torch.tensor((ebsenr_one-1)/2/ebsenr_one, device=device, dtype=torch.float32)  # 直接在 GPU 上定义 ebr\n", "ebr_two = torch.tensor((ebsenr_two-1)/2/ebsenr_two, device=device, dtype=torch.float32)\n"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["def solve(N, q, b):\n", "    i, j = np.meshgrid(np.arange(1, N+1), np.arange(1, N+1), indexing='ij')\n", "    x = (i - 1/2).flatten() * b\n", "    y = (j - 1/2).flatten() * b\n", "    # 将坐标转换为张量并移动到 GPU\n", "    x = torch.tensor(x, device=device, dtype=torch.float32)\n", "    y = torch.tensor(y, device=device, dtype=torch.float32)\n", "    # 将 d 和 z 转换为张量\n", "    d_one_tensor = torch.tensor(d_one, device=device, dtype=torch.float32)\n", "    d_two_tensor = torch.tensor(d_two, device=device, dtype=torch.float32)\n", "    z_tensor = torch.tensor(z, device=device, dtype=torch.float32)\n", "\n", "    # 计算距离矩阵\n", "    dist = torch.sqrt((x[:, None] - x[None, :])**2 + (y[:, None] - y[None, :])**2)\n", "    dist_d_one = torch.sqrt(dist**2 + d_one_tensor**2)\n", "    dist_d_two = torch.sqrt(dist**2 + d_two_tensor**2)\n", "    dist_z = torch.sqrt(dist**2 + z_tensor**2)\n", "    dist_dz_one = torch.sqrt(dist**2 + (d_one_tensor + z_tensor)**2)\n", "    dist_dz_two = torch.sqrt(dist**2 + (d_two_tensor + z_tensor)**2)\n", "    dist_dz = torch.sqrt(dist**2 + (d_one_tensor + d_two_tensor + z_tensor)**2)\n", "\n", "    # 创建矩阵\n", "    lt = torch.where(dist == 0, 4 * b * 0.8814, b**2 / dist)\n", "    ltd_one = torch.where(dist_d_one < b, 2 * np.pi * (b / 2) * (-d_one_tensor / (b / 2) + torch.sqrt((d_one_tensor / (b / 2))**2 + 4 / np.pi)), b**2 / dist_d_one)\n", "    ltd_two = torch.where(dist_d_two < b, 2 * np.pi * (b / 2) * (-d_two_tensor / (b / 2) + torch.sqrt((d_two_tensor / (b / 2))**2 + 4 / np.pi)), b**2 / dist_d_two)\n", "    ltz = torch.where(dist_z < b, 2 * np.pi * (b / 2) * (-z_tensor / (b / 2) + torch.sqrt((z_tensor / (b / 2))**2 + 4 / np.pi)), b**2 / dist_z)\n", "    ltdz_one = torch.where(dist_dz_one < b, 2 * np.pi * (b / 2) * (-(d_one_tensor+z_tensor) / (b / 2) + torch.sqrt(((d_one_tensor+z_tensor) / (b / 2))**2 + 4 / np.pi)), b**2 / dist_dz_one)\n", "    ltdz_two = torch.where(dist_dz_two < b, 2 * np.pi * (b / 2) * (-(d_two_tensor+z_tensor) / (b / 2) + torch.sqrt(((d_two_tensor+z_tensor) / (b / 2))**2 + 4 / np.pi)), b**2 / dist_dz_two)\n", "    ltdz = torch.where(dist_dz < b, 2 * np.pi * (b / 2) * (-(d_one_tensor+d_two_tensor+z_tensor) / (b / 2) + torch.sqrt(((d_one_tensor+d_two_tensor+z_tensor) / (b / 2))**2 + 4 / np.pi)), b**2 / dist_dz)\n", "\n", "    A = lt-ebr_two*(lt-ltd_two)+ebr_one*(ltdz-ltdz_two)\n", "    B = ltdz-ebr_two*(ltd_two-lt)-ebr_one*(ltdz-ltdz_two)\n", "    D = lt-ebr_one*(lt-ltd_one)+ebr_two*(ltdz-ltdz_one)\n", "    E = ltdz-ebr_one*(ltd_one-lt)-ebr_two*(ltdz-ltdz_one)\n", "    # 将矩阵合成为一个矩阵\n", "    H_top = torch.cat((A, B), dim=1)\n", "    H_bottom = torch.cat((E, D), dim=1)\n", "    H = torch.cat((H_top, H_bottom), dim=0)\n", "    # 创建一个全为 -1 的列向量\n", "    col_neg_ones = -torch.ones((H.shape[0], 1), device=device, dtype=torch.float32)\n", "    # 创建一个全为 1 的行向量\n", "    row_ones = torch.ones((1, H.shape[1] + 1), device=device, dtype=torch.float32)\n", "    # 将列向量添加到矩阵 H 的最右侧\n", "    H = torch.hstack((H, col_neg_ones))\n", "    # 将行向量添加到矩阵 H 的最底部\n", "    H = torch.vstack((H, row_ones))\n", "    # 将右下角的交界处设置为 0\n", "    H[-1, -1] = 0\n", "\n", "    sigmat_one = torch.full((N**2,), rt, device=device, dtype=torch.float32)  # 确保 sigmat 已经正确初始化\n", "    sigmat_two = torch.full((N**2,), -rt, device=device, dtype=torch.float32)  # 确保 sigmat 已经正确初始化\n", "    # 计算矩阵 C 和 F 乘以向量 sigmat\n", "    C_sigmat = -torch.matmul(ltdz_two, sigmat_one)-torch.matmul(ltd_two, sigmat_two)\n", "    F_sigmat = -torch.matmul(ltdz_one, sigmat_two)-torch.matmul(ltd_one, sigmat_one)\n", "    # 合并结果向量，并在最后添加一个元素 10000\n", "    result_vector = torch.cat((C_sigmat, F_sigmat, torch.tensor([q/b**2], device=device, dtype=torch.float32)))\n", "   \n", "    # 求解 Hx = result_vector\n", "    x_solution = torch.linalg.solve(H, result_vector)\n", "    return x_solution"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[ 9.6196729e-01  9.6818739e-01  9.6808505e-01 ... -9.6819067e-01\n", " -9.6196294e-01 -1.0695962e-05]\n"]}], "source": ["N=50\n", "b=l/N\n", "q = 0\n", "x_solution = solve(N, q, b)\n", "\n", "# 将张量转换为 NumPy 数组\n", "x_numpy = x_solution.detach().cpu().numpy()\n", "print(x_numpy)\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2435.2589750289917\n"]}], "source": ["# 直接打印x_numpy前2500个元素的和\n", "def sum_vector(vector):\n", "    \"\"\"求解向量前2500个元素的和\n", "\n", "    参数:\n", "    vector (list): 输入的向量\n", "\n", "    返回值:\n", "    float: 向量前2500个元素的和\n", "    \"\"\"\n", "    return sum(vector[:2500])\n", "    print(sum_vector(x_numpy))\n", "\n", "# 计算前 2500 个元素的和\n", "print(sum_vector(x_numpy))\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["-2435.2589789032936\n"]}], "source": ["# 直接打印x_numpy前2500个元素的和\n", "def sum_vector(vector):\n", "    \"\"\"求解向量前2500个元素的和\n", "\n", "    参数:\n", "    vector (list): 输入的向量\n", "\n", "    返回值:\n", "    float: 向量前2500个元素的和\n", "    \"\"\"\n", "    return sum(vector[2500:5000])\n", "    print(sum_vector(x_numpy))\n", "\n", "# 计算前 2500 个元素的和\n", "print(sum_vector(x_numpy))"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(5001,)\n"]}], "source": ["print(x_numpy.shape)"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# 将解向量重塑为矩阵\n", "#分别将解向量的前2500个元素和随后2500个元素分别reshape为N行N列的矩阵\n", "x_matrix1 = x_numpy[:2500].reshape(N, N)\n", "x_matrix2 = x_numpy[2500:5000].reshape(N, N)\n", "#分别绘制两个矩阵的热力图\n", "sns.heatmap(x_matrix1, cmap='coolwarm', cbar=False)\n", "sns.heatmap(x_matrix1, cmap='coolwarm', annot=False, fmt='.2f', cbar=True)\n", "plt.show()\n", "sns.heatmap(x_matrix2, cmap='coolwarm', cbar=False)\n", "sns.heatmap(x_matrix2, cmap='coolwarm', annot=False, fmt='.2f', cbar=True)\n", "plt.show()\n", "\n", "\n", "\n"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 2}