import torch
import numpy as np

def compute_solution(N, b, rt, d, z, ebsenr, device=None):
    """
    计算给定参数下的x_solution数值解
    
    参数:
    N (int): 网格大小参数
    b (float): 基础尺度参数
    rt (float): sigma参数
    d (float): 深度参数
    z (float): 高度参数
    ebsenr (float): epsilon参数
    device (str, optional): 计算设备 ('cuda' 或 'cpu')，默认自动选择
    
    返回:
    torch.Tensor: x_solution 解向量
    """
    # 检查是否有可用的 GPU
    if device is None:
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    else:
        device = torch.device(device)
    
    q = rt * N * N
    ebr = torch.tensor(-(ebsenr - 1) / (ebsenr * 2), device=device, dtype=torch.float32)

    i, j = np.meshgrid(np.arange(1, N+1), np.arange(1, N+1), indexing='ij')
    x = (i - 1/2).flatten() * b
    y = (j - 1/2).flatten() * b

    # 将坐标转换为张量并移动到 GPU
    x = torch.tensor(x, device=device, dtype=torch.float32)
    y = torch.tensor(y, device=device, dtype=torch.float32)

    # 将 d 和 z 转换为张量
    d_tensor = torch.tensor(d, device=device, dtype=torch.float32)
    z_tensor = torch.tensor(z, device=device, dtype=torch.float32)

    # 计算距离矩阵
    dist = torch.sqrt((x[:, None] - x[None, :])**2 + (y[:, None] - y[None, :])**2)
    dist_d = torch.sqrt(dist**2 + d_tensor**2)
    dist_z = torch.sqrt(dist**2 + z_tensor**2)
    dist_dz = torch.sqrt(dist**2 + (d_tensor + z_tensor)**2)

    # 创建矩阵
    lt = torch.where(dist == 0, 4 * b * 0.8814, b**2 / dist)
    ltd = torch.where(dist_d < b, 2 * np.pi * (b / 2) * (-d_tensor / (b / 2) + torch.sqrt((d_tensor / (b / 2))**2 + 4 / np.pi)), b**2 / dist_d)
    ltz = torch.where(dist_z < b, 2 * np.pi * (b / 2) * (-z_tensor / (b / 2) + torch.sqrt((z_tensor / (b / 2))**2 + 4 / np.pi)), b**2 / dist_z)
    ltdz = torch.where(dist_dz < b, 2 * np.pi * (b / 2) * (-(d_tensor + z_tensor) / (b / 2) + torch.sqrt(((d_tensor + z_tensor) / (b / 2))**2 + 4 / np.pi)), b**2 / dist_dz)

    A = ltdz - ebr * (lt - ltd)
    B = lt + ebr * (lt - ltd)
    D = lt + ebr * (ltz - ltdz)
    E = ltdz - ebr * (ltz - ltdz)
    # 将矩阵合成为一个矩阵
    H_top = torch.cat((A, B), dim=1)
    H_bottom = torch.cat((D, E), dim=1)
    H = torch.cat((H_top, H_bottom), dim=0)
    # 创建一个全为 -1 的列向量
    col_neg_ones = -torch.ones((H.shape[0], 1), device=device, dtype=torch.float32)
    # 创建一个全为 1 的行向量
    row_ones = torch.ones((1, H.shape[1] + 1), device=device, dtype=torch.float32)
    # 将列向量添加到矩阵 H 的最右侧
    H = torch.hstack((H, col_neg_ones))
    # 将行向量添加到矩阵 H 的最底部
    H = torch.vstack((H, row_ones))
    # 将右下角的交界处设置为 0
    H[-1, -1] = 0

    C = ltd - ebr * (lt - ltd)
    F = ltz + ebr * (ltz - ltdz)
    sigmat = torch.full((N**2,), rt, device=device, dtype=torch.float32)
    # 计算矩阵 C 和 F 乘以向量 sigmat
    C_sigmat = torch.matmul(C, sigmat)
    F_sigmat = torch.matmul(F, sigmat)
    # 合并结果向量，并在最后添加一个元素
    result_vector = torch.cat((C_sigmat, F_sigmat, torch.tensor([q], device=device, dtype=torch.float32)))

    # 求解 Hx = result_vector
    x_solution = torch.linalg.solve(H, result_vector)

    # 提取解向量的前 N^2 个元素
    x_solution_up = x_solution[:N**2]
    # 提取解向量的中间 N^2 个元素
    x_solution_down = x_solution[N**2:N**2 + N**2]
    # 提取解向量的最后一个元素
    voltage = x_solution[-1]

    return x_solution
