#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
电磁场仿真智能体测试脚本
"""

import matplotlib.pyplot as plt
from electromagnetic_agent import ElectromagneticAgent

def test_basic_functionality():
    """测试基本功能"""
    print("🧪 测试电磁场仿真智能体基本功能...")
    
    # 初始化智能体
    api_key = "sk-darna2nrdmzrwbcx"
    agent = ElectromagneticAgent(api_key)
    
    # 测试用例
    test_cases = [
        {
            "name": "电荷分布仿真",
            "input": "计算电荷分布，长度网格30，宽度网格30，b=1，rt=1，d=0.6，z=0.6，epsilon=2.2",
            "expected_type": "charge_distribution"
        },
        {
            "name": "转移电荷仿真", 
            "input": "分析转移电荷变化，网格大小30x30，时间从0到5，步数50，运动方程0.6+0.4*sin(t)",
            "expected_type": "transfer_charge"
        },
        {
            "name": "电流输出仿真",
            "input": "计算电流输出，N_length=30，N_width=30，时间范围0-3，步数30",
            "expected_type": "electric_current"
        }
    ]
    
    results = []
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n--- 测试 {i}: {test_case['name']} ---")
        print(f"输入: {test_case['input']}")
        
        try:
            response = agent.process_request(test_case['input'])
            
            if response['success']:
                print(f"✅ 成功")
                print(f"   仿真类型: {response['simulation_type']}")
                print(f"   参数数量: {len(response['parameters'])}")
                
                # 验证仿真类型是否正确
                if response['simulation_type'] == test_case['expected_type']:
                    print(f"   ✅ 仿真类型识别正确")
                else:
                    print(f"   ⚠️  仿真类型识别可能有误，期望: {test_case['expected_type']}")
                
                # 显示图表（如果有）
                if response['simulation_type'] in ['transfer_charge', 'electric_current']:
                    fig, ax = response['result']
                    plt.show()
                
                results.append(True)
            else:
                print(f"❌ 失败: {response['error']}")
                results.append(False)
                
        except Exception as e:
            print(f"❌ 异常: {str(e)}")
            results.append(False)
    
    # 总结
    success_count = sum(results)
    total_count = len(results)
    print(f"\n📊 测试总结: {success_count}/{total_count} 个测试通过")
    
    if success_count == total_count:
        print("🎉 所有测试通过！智能体工作正常")
    else:
        print("⚠️  部分测试失败，请检查配置")

def test_parameter_parsing():
    """测试参数解析功能"""
    print("\n🔍 测试参数解析功能...")
    
    from parameter_parser import ParameterParser
    parser = ParameterParser()
    
    test_texts = [
        "N_length=50, N_width=60, b=1.5, rt=-1.2, d=0.8",
        "长度网格100，宽度网格80，基础尺度2.0，sigma参数1.5",
        "时间从0到10，步数200，运动方程0.5+0.3*sin(t)"
    ]
    
    for text in test_texts:
        print(f"\n输入: {text}")
        params = parser.parse_parameters(text)
        print("解析结果:")
        for key, value in params.items():
            if key != 'f':  # 跳过函数对象
                print(f"  {key}: {value}")

if __name__ == "__main__":
    print("🚀 启动电磁场仿真智能体测试")
    print("=" * 50)
    
    # 测试参数解析
    test_parameter_parsing()
    
    # 测试基本功能
    test_basic_functionality()
    
    print("\n✨ 测试完成！")
