import torch
import numpy as np
import seaborn as sns
import matplotlib.pyplot as plt
from math import sin,pi

from src.e_long import compute_solution_long

N_length = 60
N_width = 60
b = 1
rt = 1
d = 0.6
z = 0.6
ebsenr = 2.2

x_solution = compute_solution_long(N_length, N_width, b, rt, d, z, ebsenr)
print(x_solution)

x_solution = x_solution.detach().cpu().numpy()

# 提取解向量的前 N^2 个元素
x_solution_up = x_solution[:N_length*N_width]
# 求和
sum_x_solution_up = np.sum(x_solution_up)*b**2
print(sum_x_solution_up)

# 将解向量重塑为矩阵
#分别将解向量的前2500个元素和随后2500个元素分别reshape为N行N列的矩阵
x_matrix1 = x_solution[:N_length * N_width].reshape(N_length, N_width)
x_matrix2 = x_solution[N_length * N_width:N_length * N_width + N_length * N_width].reshape(N_length, N_width)
print(x_matrix1)
print(x_matrix2)
#分别绘制两个矩阵的热力图
sns.heatmap(x_matrix1, cmap='coolwarm', cbar=False)
sns.heatmap(x_matrix1, cmap='coolwarm', annot=False, fmt='.2f', cbar=True)
plt.show()
sns.heatmap(x_matrix2, cmap='coolwarm', cbar=False)
sns.heatmap(x_matrix2, cmap='coolwarm', annot=False, fmt='.2f', cbar=True)
plt.show()


from src.i_long import compute_charge_sum
f=lambda t: 0.6+0.4*np.sin(t)
t = 0
sum_x_solution_up=compute_charge_sum(N_length, N_width, b, rt, d, t, f, ebsenr)
print(sum_x_solution_up)

from src.i_long import plot_electric_currents
f=lambda t: 0.6+0.4*np.sin(t)
t_start = 0
t_end = 8
T = 100

plot_electric_currents(N_length, N_width, b, rt, d, t_start, t_end, T, f, ebsenr)

from src.i_long import plot_transfer_charges
f=lambda t: 0.6+0.4*np.sin(t)
t_start = 0
t_end = 8
T = 100

plot_transfer_charges(N_length, N_width, b, rt, d, t_start, t_end, T, f, ebsenr)

from src.potential import compute_voltage
potential_distribution = compute_voltage(N_length, N_width, b, rt, d, z, ebsenr)
print(potential_distribution)

print(potential_distribution[24:25, 14:15, :])