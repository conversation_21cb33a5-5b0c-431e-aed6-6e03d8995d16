{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import torch\n", "import numpy as np"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# 检查是否有可用的 GPU\n", "device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["N = 100\n", "b = 1\n", "rt = 1\n", "d = 5\n", "z = 5\n", "q = 10000/b**2\n", "ebsenr = 2.2\n", "ebr = torch.tensor(-(ebsenr - 1) / (ebsenr * 2), device=device, dtype=torch.float32)  # 直接在 GPU 上定义 ebr"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[ 0.5  0.5  0.5 ... 99.5 99.5 99.5]\n", "[ 0.5  1.5  2.5 ... 97.5 98.5 99.5]\n"]}], "source": ["i, j = np.meshgrid(np.arange(1, N+1), np.arange(1, N+1), indexing='ij')\n", "x = (i - 1/2).flatten() * b\n", "y = (j - 1/2).flatten() * b\n", "print(x)\n", "print(y)\n", "# 将坐标转换为张量并移动到 GPU\n", "x = torch.tensor(x, device=device, dtype=torch.float32)\n", "y = torch.tensor(y, device=device, dtype=torch.float32)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["tensor([[3.5256, 1.0000, 0.5000,  ..., 0.0072, 0.0072, 0.0071],\n", "        [1.0000, 3.5256, 1.0000,  ..., 0.0073, 0.0072, 0.0072],\n", "        [0.5000, 1.0000, 3.5256,  ..., 0.0073, 0.0073, 0.0072],\n", "        ...,\n", "        [0.0072, 0.0073, 0.0073,  ..., 3.5256, 1.0000, 0.5000],\n", "        [0.0072, 0.0072, 0.0073,  ..., 1.0000, 3.5256, 1.0000],\n", "        [0.0071, 0.0072, 0.0072,  ..., 0.5000, 1.0000, 3.5256]],\n", "       device='cuda:0')\n", "tensor([[0.1994, 0.1961, 0.1857,  ..., 0.0072, 0.0072, 0.0071],\n", "        [0.1961, 0.1994, 0.1961,  ..., 0.0072, 0.0072, 0.0072],\n", "        [0.1857, 0.1961, 0.1994,  ..., 0.0073, 0.0072, 0.0072],\n", "        ...,\n", "        [0.0072, 0.0072, 0.0073,  ..., 0.1994, 0.1961, 0.1857],\n", "        [0.0072, 0.0072, 0.0072,  ..., 0.1961, 0.1994, 0.1961],\n", "        [0.0071, 0.0072, 0.0072,  ..., 0.1857, 0.1961, 0.1994]],\n", "       device='cuda:0')\n", "tensor([[0.1994, 0.1961, 0.1857,  ..., 0.0072, 0.0072, 0.0071],\n", "        [0.1961, 0.1994, 0.1961,  ..., 0.0072, 0.0072, 0.0072],\n", "        [0.1857, 0.1961, 0.1994,  ..., 0.0073, 0.0072, 0.0072],\n", "        ...,\n", "        [0.0072, 0.0072, 0.0073,  ..., 0.1994, 0.1961, 0.1857],\n", "        [0.0072, 0.0072, 0.0072,  ..., 0.1961, 0.1994, 0.1961],\n", "        [0.0071, 0.0072, 0.0072,  ..., 0.1857, 0.1961, 0.1994]],\n", "       device='cuda:0')\n", "tensor([[0.0999, 0.0995, 0.0981,  ..., 0.0072, 0.0072, 0.0071],\n", "        [0.0995, 0.0999, 0.0995,  ..., 0.0072, 0.0072, 0.0072],\n", "        [0.0981, 0.0995, 0.0999,  ..., 0.0073, 0.0072, 0.0072],\n", "        ...,\n", "        [0.0072, 0.0072, 0.0073,  ..., 0.0999, 0.0995, 0.0981],\n", "        [0.0072, 0.0072, 0.0072,  ..., 0.0995, 0.0999, 0.0995],\n", "        [0.0071, 0.0072, 0.0072,  ..., 0.0981, 0.0995, 0.0999]],\n", "       device='cuda:0')\n"]}], "source": ["# 将 d 和 z 转换为张量\n", "d_tensor = torch.tensor(d, device=device, dtype=torch.float32)\n", "z_tensor = torch.tensor(z, device=device, dtype=torch.float32)\n", "\n", "# 计算距离矩阵\n", "dist = torch.sqrt((x[:, None] - x[None, :])**2 + (y[:, None] - y[None, :])**2)\n", "dist_d = torch.sqrt(dist**2 + d_tensor**2)\n", "dist_z = torch.sqrt(dist**2 + z_tensor**2)\n", "dist_dz = torch.sqrt(dist**2 + (d_tensor + z_tensor)**2)\n", "\n", "# 创建矩阵\n", "lt = torch.where(dist == 0, 4 * b * 0.8814, b**2 / dist)\n", "ltd = torch.where(dist == 0, 2 * np.pi * (b / 2) * (-d_tensor / (b / 2) + torch.sqrt((d_tensor / (b / 2))**2 + 4 / np.pi)), b**2 / dist_d)\n", "ltz = torch.where(dist == 0, 2 * np.pi * (b / 2) * (-z_tensor / (b / 2) + torch.sqrt((z_tensor / (b / 2))**2 + 4 / np.pi)), b**2 / dist_z)\n", "ltdz = torch.where(dist == 0, 2 * np.pi * (b / 2) * (-(d_tensor + z_tensor) / (b / 2) + torch.sqrt(((d_tensor + z_tensor) / (b / 2))**2 + 4 / np.pi)), b**2 / dist_dz)\n", "\n", "print(lt)\n", "print(ltd)\n", "print(ltz)\n", "print(ltdz)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["tensor([[ 1.0071,  0.3187,  0.1838,  ...,  0.0072,  0.0071, -1.0000],\n", "        [ 0.3187,  1.0071,  0.3187,  ...,  0.0072,  0.0072, -1.0000],\n", "        [ 0.1838,  0.3187,  1.0071,  ...,  0.0073,  0.0072, -1.0000],\n", "        ...,\n", "        [ 0.0072,  0.0072,  0.0072,  ...,  0.1270,  0.1259, -1.0000],\n", "        [ 0.0071,  0.0072,  0.0072,  ...,  0.1259,  0.1270, -1.0000],\n", "        [ 1.0000,  1.0000,  1.0000,  ...,  1.0000,  1.0000,  0.0000]],\n", "       device='cuda:0')\n"]}], "source": ["A = ltdz - ebr * (lt - ltd)\n", "B = lt + ebr * (lt - ltd)\n", "D = lt + ebr * (ltz - ltdz)\n", "E = ltdz - ebr * (ltz - ltdz)\n", "# 将矩阵合成为一个矩阵\n", "H_top = torch.cat((A, B), dim=1)\n", "H_bottom = torch.cat((D, E), dim=1)\n", "H = torch.cat((H_top, H_bottom), dim=0)\n", "# 创建一个全为 -1 的列向量\n", "col_neg_ones = -torch.ones((H.shape[0], 1), device=device, dtype=torch.float32)\n", "# 创建一个全为 1 的行向量\n", "row_ones = torch.ones((1, H.shape[1] + 1), device=device, dtype=torch.float32)\n", "# 将列向量添加到矩阵 H 的最右侧\n", "H = torch.hstack((H, col_neg_ones))\n", "# 将行向量添加到矩阵 H 的最底部\n", "H = torch.vstack((H, row_ones))\n", "# 将右下角的交界处设置为 0\n", "H[-1, -1] = 0\n", "print(H)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["tensor([  174.6342,   178.0621,   181.1816,  ...,   171.9361,   169.2592,\n", "        10000.0000], device='cuda:0')\n"]}], "source": ["C = ltd - ebr * (lt - ltd)\n", "F = ltz + ebr * (ltz - ltdz)\n", "sigmat = torch.full((N**2,), rt, device=device, dtype=torch.float32)  # 确保 sigmat 已经正确初始化\n", "# 计算矩阵 C 和 F 乘以向量 sigmat\n", "C_sigmat = torch.matmul(C, sigmat)\n", "F_sigmat = torch.matmul(F, sigmat)\n", "# 合并结果向量，并在最后添加一个元素 10000\n", "result_vector = torch.cat((C_sigmat, F_sigmat, torch.tensor([q], device=device, dtype=torch.float32)))\n", "print(result_vector)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["tensor([0.5089, 0.3791, 0.3777,  ..., 0.7541, 0.8839, 1.5470], device='cuda:0')\n"]}], "source": ["# 求解 Hx = result_vector\n", "x_solution = torch.linalg.solve(H, result_vector)\n", "print(x_solution)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["20001\n", "Epoch [200/20000], Loss: 60.7954\n", "Epoch [400/20000], Loss: 45.1195\n", "Epoch [600/20000], Loss: 43.8382\n", "Epoch [800/20000], Loss: 30.2031\n", "Epoch [1000/20000], Loss: 28.6678\n", "Epoch [1200/20000], Loss: 23.2090\n", "Epoch [1400/20000], Loss: 19.2680\n", "Epoch [1600/20000], Loss: 15.9095\n", "Epoch [1800/20000], Loss: 14.9952\n", "Epoch [2000/20000], Loss: 12.8843\n", "Epoch [2200/20000], Loss: 9.8724\n", "Epoch [2400/20000], Loss: 8.3006\n", "Epoch [2600/20000], Loss: 7.9935\n", "Epoch [2800/20000], Loss: 6.4362\n", "Epoch [3000/20000], Loss: 5.1475\n", "Epoch [3200/20000], Loss: 3.9240\n", "Epoch [3400/20000], Loss: 3.7890\n", "Epoch [3600/20000], Loss: 3.3084\n", "Epoch [3800/20000], Loss: 2.7145\n", "Epoch [4000/20000], Loss: 2.0100\n", "Epoch [4200/20000], Loss: 1.7352\n", "Epoch [4400/20000], Loss: 1.3435\n", "Epoch [4600/20000], Loss: 1.1872\n", "Epoch [4800/20000], Loss: 1.0323\n", "Epoch [5000/20000], Loss: 0.7770\n", "Epoch [5200/20000], Loss: 0.5235\n", "Epoch [5400/20000], Loss: 0.4673\n", "Epoch [5600/20000], Loss: 0.3413\n", "Epoch [5800/20000], Loss: 0.2713\n", "Epoch [6000/20000], Loss: 0.2175\n", "Epoch [6200/20000], Loss: 0.1849\n", "Epoch [6400/20000], Loss: 0.1297\n", "Epoch [6600/20000], Loss: 0.1049\n", "Epoch [6800/20000], Loss: 0.0728\n", "Epoch [7000/20000], Loss: 0.0649\n", "Epoch [7200/20000], Loss: 0.0652\n", "Epoch [7400/20000], Loss: 0.0431\n", "Epoch [7600/20000], Loss: 0.0268\n", "Epoch [7800/20000], Loss: 0.0236\n", "Epoch [8000/20000], Loss: 0.0313\n", "Epoch [8200/20000], Loss: 0.0153\n", "Epoch [8400/20000], Loss: 0.0273\n", "Epoch [8600/20000], Loss: 0.0131\n", "Epoch [8800/20000], Loss: 0.0107\n", "Epoch [9000/20000], Loss: 0.0924\n", "Epoch [9200/20000], Loss: 0.0103\n", "Epoch [9400/20000], Loss: 0.0062\n", "Epoch [9600/20000], Loss: 0.0072\n", "Epoch [9800/20000], Loss: 0.0131\n", "Epoch [10000/20000], Loss: 0.0323\n", "Epoch [10200/20000], Loss: 0.0030\n", "Epoch [10400/20000], Loss: 0.0048\n", "Epoch [10600/20000], Loss: 0.0020\n", "Epoch [10800/20000], Loss: 0.0027\n", "Epoch [11000/20000], Loss: 0.0023\n", "Epoch [11200/20000], Loss: 0.0032\n", "Epoch [11400/20000], Loss: 0.0029\n", "Epoch [11600/20000], Loss: 0.0012\n", "Epoch [11800/20000], Loss: 0.0014\n", "Epoch [12000/20000], Loss: 0.0030\n", "Epoch [12200/20000], Loss: 0.0452\n", "Epoch [12400/20000], Loss: 0.0009\n", "Epoch [12600/20000], Loss: 0.0057\n", "Epoch [12800/20000], Loss: 0.0007\n", "Epoch [13000/20000], Loss: 0.0016\n", "Epoch [13200/20000], Loss: 0.0007\n"]}, {"ename": "KeyboardInterrupt", "evalue": "", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mKeyboardInterrupt\u001b[0m                         <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[9], line 23\u001b[0m\n\u001b[0;32m     21\u001b[0m \u001b[38;5;66;03m# 计算损失和梯度\u001b[39;00m\n\u001b[0;32m     22\u001b[0m loss \u001b[38;5;241m=\u001b[39m \u001b[38;5;241m0.5\u001b[39m \u001b[38;5;241m*\u001b[39m torch\u001b[38;5;241m.\u001b[39mnorm(torch\u001b[38;5;241m.\u001b[39mmatmul(H_batch, xt) \u001b[38;5;241m-\u001b[39m result_vector_batch) \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39m \u001b[38;5;241m2\u001b[39m\n\u001b[1;32m---> 23\u001b[0m \u001b[43mloss\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mbackward\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[0;32m     24\u001b[0m optimizer\u001b[38;5;241m.\u001b[39mstep()\n\u001b[0;32m     25\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m (epoch\u001b[38;5;241m+\u001b[39m\u001b[38;5;241m1\u001b[39m) \u001b[38;5;241m%\u001b[39m \u001b[38;5;241m200\u001b[39m \u001b[38;5;241m==\u001b[39m \u001b[38;5;241m0\u001b[39m:\n", "File \u001b[1;32md:\\anaconda\\envs\\xypytorch\\lib\\site-packages\\torch\\_tensor.py:521\u001b[0m, in \u001b[0;36mTensor.backward\u001b[1;34m(self, gradient, retain_graph, create_graph, inputs)\u001b[0m\n\u001b[0;32m    511\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m has_torch_function_unary(\u001b[38;5;28mself\u001b[39m):\n\u001b[0;32m    512\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m handle_torch_function(\n\u001b[0;32m    513\u001b[0m         Tensor\u001b[38;5;241m.\u001b[39mbackward,\n\u001b[0;32m    514\u001b[0m         (\u001b[38;5;28mself\u001b[39m,),\n\u001b[1;32m   (...)\u001b[0m\n\u001b[0;32m    519\u001b[0m         inputs\u001b[38;5;241m=\u001b[39minputs,\n\u001b[0;32m    520\u001b[0m     )\n\u001b[1;32m--> 521\u001b[0m \u001b[43mtorch\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mautograd\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mbackward\u001b[49m\u001b[43m(\u001b[49m\n\u001b[0;32m    522\u001b[0m \u001b[43m    \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mgradient\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mretain_graph\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mcreate_graph\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43minputs\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43minputs\u001b[49m\n\u001b[0;32m    523\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[1;32md:\\anaconda\\envs\\xypytorch\\lib\\site-packages\\torch\\autograd\\__init__.py:289\u001b[0m, in \u001b[0;36mbackward\u001b[1;34m(tensors, grad_tensors, retain_graph, create_graph, grad_variables, inputs)\u001b[0m\n\u001b[0;32m    284\u001b[0m     retain_graph \u001b[38;5;241m=\u001b[39m create_graph\n\u001b[0;32m    286\u001b[0m \u001b[38;5;66;03m# The reason we repeat the same comment below is that\u001b[39;00m\n\u001b[0;32m    287\u001b[0m \u001b[38;5;66;03m# some Python versions print out the first line of a multi-line function\u001b[39;00m\n\u001b[0;32m    288\u001b[0m \u001b[38;5;66;03m# calls in the traceback and some print out the last line\u001b[39;00m\n\u001b[1;32m--> 289\u001b[0m \u001b[43m_engine_run_backward\u001b[49m\u001b[43m(\u001b[49m\n\u001b[0;32m    290\u001b[0m \u001b[43m    \u001b[49m\u001b[43mtensors\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    291\u001b[0m \u001b[43m    \u001b[49m\u001b[43mgrad_tensors_\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    292\u001b[0m \u001b[43m    \u001b[49m\u001b[43mretain_graph\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    293\u001b[0m \u001b[43m    \u001b[49m\u001b[43mcreate_graph\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    294\u001b[0m \u001b[43m    \u001b[49m\u001b[43minputs\u001b[49m\u001b[43m,\u001b[49m\n\u001b[0;32m    295\u001b[0m \u001b[43m    \u001b[49m\u001b[43mallow_unreachable\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mTrue\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[0;32m    296\u001b[0m \u001b[43m    \u001b[49m\u001b[43maccumulate_grad\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mTrue\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[0;32m    297\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[1;32md:\\anaconda\\envs\\xypytorch\\lib\\site-packages\\torch\\autograd\\graph.py:769\u001b[0m, in \u001b[0;36m_engine_run_backward\u001b[1;34m(t_outputs, *args, **kwargs)\u001b[0m\n\u001b[0;32m    767\u001b[0m     unregister_hooks \u001b[38;5;241m=\u001b[39m _register_logging_hooks_on_whole_graph(t_outputs)\n\u001b[0;32m    768\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m--> 769\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m Variable\u001b[38;5;241m.\u001b[39m_execution_engine\u001b[38;5;241m.\u001b[39mrun_backward(  \u001b[38;5;66;03m# Calls into the C++ engine to run the backward pass\u001b[39;00m\n\u001b[0;32m    770\u001b[0m         t_outputs, \u001b[38;5;241m*\u001b[39margs, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mkwargs\n\u001b[0;32m    771\u001b[0m     )  \u001b[38;5;66;03m# Calls into the C++ engine to run the backward pass\u001b[39;00m\n\u001b[0;32m    772\u001b[0m \u001b[38;5;28;01mfinally\u001b[39;00m:\n\u001b[0;32m    773\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m attach_logging_hooks:\n", "\u001b[1;31mKeyboardInterrupt\u001b[0m: "]}], "source": ["# 初始化待求解的变量 xt，并设置 requires_grad=True\n", "xt = torch.cat((\n", "    torch.full((N**2,), rt * d / (ebsenr * z + d), device=device, dtype=torch.float32),\n", "    torch.full((N**2,), ebsenr * rt * z / (ebsenr * z + d), device=device, dtype=torch.float32),\n", "    torch.tensor([rt * z * d / (ebsenr * z + d)], device=device, dtype=torch.float32)), dim=0)\n", "xt.requires_grad_(True)\n", "\n", "# 定义优化器\n", "optimizer = torch.optim.AdamW([xt], lr=0.0001)\n", "batch_size = 5000  # 设置小批量的大小\n", "total_size = H.shape[0]\n", "print(total_size)\n", "\n", "# 迭代优化\n", "for epoch in range(60000):\n", "    optimizer.zero_grad()\n", "    # 随机选择部分样本数据\n", "    indices = torch.randint(0, total_size, (batch_size,), device=device)\n", "    H_batch = H[indices]\n", "    result_vector_batch = result_vector[indices]\n", "    # 计算损失和梯度\n", "    loss = 0.5 * torch.norm(torch.matmul(H_batch, xt) - result_vector_batch) ** 2\n", "    loss.backward()\n", "    optimizer.step()\n", "    if (epoch+1) % 200 == 0:\n", "        print(f'Epoch [{epoch+1}/20000], Loss: {loss.item():.4f}')"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Optimized xt: [0.5097088  0.37864253 0.37790498 ... 0.75589323 0.88094854 1.5465598 ]\n"]}], "source": ["# 输出优化后的 xt\n", "print(\"Optimized xt:\", xt.detach().cpu().numpy())"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Error between x_solution and xt: 0.0284\n", "Realtive error between x_solution and xt: 0.0004\n", "Relative error between x_solution and xt: tensor([0.0016, 0.0011, 0.0006,  ..., 0.0024, 0.0033, 0.0003], device='cuda:0',\n", "       grad_fn=<DivBackward0>)\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1000x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1000x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import seaborn as sns\n", "\n", "import matplotlib.pyplot as plt\n", "\n", "# 计算 x_solution 和 xt 的误差\n", "error = torch.norm(x_solution - xt).item()\n", "relative_error = torch.norm(x_solution - xt).item() / torch.norm(x_solution).item()\n", "print(f'Error between x_solution and xt: {error:.4f}')\n", "print(f'Realtive error between x_solution and xt: {relative_error:.4f}')\n", "\n", "# 计算 x_solution 和 xt 的相对误差\n", "relative_error = torch.abs(x_solution - xt) / x_solution\n", "print(f'Relative error between x_solution and xt: {relative_error}')\n", "\n", "# 设置 Seaborn 样式\n", "sns.set(style=\"whitegrid\")\n", "\n", "# 绘制相对误差图\n", "plt.figure(figsize=(10, 6))\n", "sns.lineplot(data=relative_error.detach().cpu().numpy())\n", "plt.title('Relative Error between x_solution and xt')\n", "plt.xlabel('Index')\n", "plt.ylabel('Relative Error')\n", "plt.show()\n", "\n", "# 绘制误差图\n", "plt.figure(figsize=(10, 6))\n", "sns.lineplot(data=x_solution.detach().cpu().numpy(), label='x_solution')\n", "sns.lineplot(data=xt.detach().cpu().numpy(), label='xt')\n", "plt.legend()\n", "plt.title('Comparison of x_solution and xt')\n", "plt.xlabel('Index')\n", "plt.ylabel('Value')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "xypytorch", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.19"}}, "nbformat": 4, "nbformat_minor": 4}