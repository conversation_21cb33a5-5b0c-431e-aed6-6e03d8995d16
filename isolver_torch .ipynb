{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import torch\n", "import numpy as np\n", "from math import sin, pi\n", "import seaborn as sns\n", "import matplotlib.pyplot as plt\n", "import pandas as pd\n", "# 检查是否有可用的 GPU\n", "device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["N = 50\n", "n = 100\n", "b = 4\n", "rt = 1.2\n", "d = 5\n", "q = rt * N**2      \n", "ebsenr = 2.2\n", "ebr = torch.tensor(-(ebsenr - 1) / (ebsenr * 2), device=device, dtype=torch.float16)  # 直接在 GPU 上定义 ebr"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["tensor([[[ 4.5391,  1.8652,  1.3145,  ...,  0.0000,  0.0000, -1.0000],\n", "         [ 1.8652,  4.5391,  1.8652,  ...,  0.0000,  0.0000, -1.0000],\n", "         [ 1.3145,  1.8652,  4.5391,  ...,  0.0000,  0.0000, -1.0000],\n", "         ...,\n", "         [ 0.0000,  0.0000,  0.0000,  ...,  1.9707,  1.7207, -1.0000],\n", "         [ 0.0000,  0.0000,  0.0000,  ...,  1.7207,  1.9707, -1.0000],\n", "         [ 1.0000,  1.0000,  1.0000,  ...,  1.0000,  1.0000,  0.0000]],\n", "\n", "        [[ 4.5039,  1.8359,  1.2969,  ...,  0.0000,  0.0000, -1.0000],\n", "         [ 1.8359,  4.5039,  1.8359,  ...,  0.0000,  0.0000, -1.0000],\n", "         [ 1.2969,  1.8359,  4.5039,  ...,  0.0000,  0.0000, -1.0000],\n", "         ...,\n", "         [ 0.0000,  0.0000,  0.0000,  ...,  1.9092,  1.6816, -1.0000],\n", "         [ 0.0000,  0.0000,  0.0000,  ...,  1.6816,  1.9092, -1.0000],\n", "         [ 1.0000,  1.0000,  1.0000,  ...,  1.0000,  1.0000,  0.0000]],\n", "\n", "        [[ 4.4688,  1.8096,  1.2803,  ...,  0.0000,  0.0000, -1.0000],\n", "         [ 1.8096,  4.4688,  1.8096,  ...,  0.0000,  0.0000, -1.0000],\n", "         [ 1.2803,  1.8096,  4.4688,  ...,  0.0000,  0.0000, -1.0000],\n", "         ...,\n", "         [ 0.0000,  0.0000,  0.0000,  ...,  1.8525,  1.6455, -1.0000],\n", "         [ 0.0000,  0.0000,  0.0000,  ...,  1.6455,  1.8525, -1.0000],\n", "         [ 1.0000,  1.0000,  1.0000,  ...,  1.0000,  1.0000,  0.0000]],\n", "\n", "        ...,\n", "\n", "        [[ 4.6523,  1.9570,  1.3682,  ...,  0.0000,  0.0000, -1.0000],\n", "         [ 1.9570,  4.6523,  1.9570,  ...,  0.0000,  0.0000, -1.0000],\n", "         [ 1.3682,  1.9570,  4.6523,  ...,  0.0000,  0.0000, -1.0000],\n", "         ...,\n", "         [ 0.0000,  0.0000,  0.0000,  ...,  2.1836,  1.8477, -1.0000],\n", "         [ 0.0000,  0.0000,  0.0000,  ...,  1.8477,  2.1836, -1.0000],\n", "         [ 1.0000,  1.0000,  1.0000,  ...,  1.0000,  1.0000,  0.0000]],\n", "\n", "        [[ 4.6133,  1.9248,  1.3506,  ...,  0.0000,  0.0000, -1.0000],\n", "         [ 1.9248,  4.6133,  1.9248,  ...,  0.0000,  0.0000, -1.0000],\n", "         [ 1.3506,  1.9248,  4.6133,  ...,  0.0000,  0.0000, -1.0000],\n", "         ...,\n", "         [ 0.0000,  0.0000,  0.0000,  ...,  2.1074,  1.8037, -1.0000],\n", "         [ 0.0000,  0.0000,  0.0000,  ...,  1.8037,  2.1074, -1.0000],\n", "         [ 1.0000,  1.0000,  1.0000,  ...,  1.0000,  1.0000,  0.0000]],\n", "\n", "        [[ 4.5742,  1.8936,  1.3320,  ...,  0.0000,  0.0000, -1.0000],\n", "         [ 1.8936,  4.5742,  1.8936,  ...,  0.0000,  0.0000, -1.0000],\n", "         [ 1.3320,  1.8936,  4.5742,  ...,  0.0000,  0.0000, -1.0000],\n", "         ...,\n", "         [ 0.0000,  0.0000,  0.0000,  ...,  2.0332,  1.7607, -1.0000],\n", "         [ 0.0000,  0.0000,  0.0000,  ...,  1.7607,  2.0332, -1.0000],\n", "         [ 1.0000,  1.0000,  1.0000,  ...,  1.0000,  1.0000,  0.0000]]],\n", "       dtype=torch.float16)\n", "torch.Size([80, 5001, 5001])\n"]}], "source": ["\n", "   # 生成 t 的取值范围\n", "t_values = torch.arange(1, n+1, device=device, dtype=torch.float16)  # shape: (n,)\n", "\n", "# 计算 z 的取值，对应每个 t\n", "z_values = 10 + 3 * torch.sin(160 * pi * t_values / 8000)  # shape: (n,)\n", "\n", "# 生成 x 和 y 坐标\n", "i, j = np.meshgrid(np.arange(1, N+1), np.arange(1, N+1), indexing='ij')\n", "x = (i - 1/2).flatten() * b  # shape: (N^2,)\n", "y = (j - 1/2).flatten() * b  # shape: (N^2,)\n", "\n", "# 将 x 和 y 转换为张量并移动到 GPU\n", "x = torch.tensor(x, device=device, dtype=torch.float16)  # shape: (N^2,)\n", "y = torch.tensor(y, device=device, dtype=torch.float16)  # shape: (N^2,)\n", "\n", "# 计算距离矩阵 dist，shape: (N^2, N^2)\n", "dist = torch.sqrt((x[:, None] - x[None, :])**2 + (y[:, None] - y[None, :])**2)  # shape: (N^2, N^2)\n", "\n", "# 为了与 z_values 进行广播，扩展 dist 的形状\n", "dist = dist.unsqueeze(0)  # shape: (1, N^2, N^2)\n", "\n", "# 将 d 转换为张量\n", "d_tensor = torch.tensor(d, device=device, dtype=torch.float16)  # 标量\n", "\n", "# 计算 dist_d，shape: (1, N^2, N^2)\n", "dist_d = torch.sqrt(dist**2 + d_tensor**2)\n", "\n", "# 扩展 z_values 的形状以进行广播\n", "z_values_expanded = z_values.view(n, 1, 1)  # shape: (n, 1, 1)\n", "\n", "# 计算 dist_z，shape: (n, N^2, N^2)\n", "dist_z = torch.sqrt(dist**2 + z_values_expanded**2)\n", "\n", "# 计算 dist_dz，shape: (n, N^2, N^2)\n", "dist_dz = torch.sqrt(dist**2 + (d_tensor + z_values_expanded)**2)\n", "\n", "# 创建矩阵 lt，shape: (1, N^2, N^2)\n", "lt = torch.where(dist == 0, 4 * b * 0.8814, b**2 / dist)\n", "\n", "# 将矩阵lt拓展为形状(n, N^2, N^2)，相当于原矩阵重复n次\n", "lt = lt.expand(n, -1, -1)  # shape: (n, N^2, N^2)\n", "\n", "# 创建矩阵 ltd，shape: (1, N^2, N^2)\n", "ltd = torch.where(dist_d ==0, 2 * pi * (b / 2) * (-d_tensor / (b / 2) + torch.sqrt((d_tensor / (b / 2))**2 + 4 / pi)), b**2 / dist_d)\n", "\n", "# 创建矩阵 ltz，shape: (n, N^2, N^2)\n", "ltz = torch.where(dist_z ==0, 2 * pi * (b / 2) * (-z_values_expanded / (b / 2) + torch.sqrt((z_values_expanded / (b / 2))**2 + 4 / pi)), b**2 / dist_z)\n", "\n", "# 创建矩阵 ltdz，shape: (n, N^2, N^2)\n", "ltdz = torch.where(dist_dz ==0, 2 * pi * (b / 2) * (-(d_tensor + z_values_expanded) / (b / 2) + torch.sqrt(((d_tensor + z_values_expanded) / (b / 2))**2 + 4 / pi)), b**2 / dist_dz)\n", "\n", "# 计算 A, B, D, E 矩阵\n", "A = ltdz - ebr * (lt - ltd)            # shape: (n, N^2, N^2)\n", "B = lt + ebr * (lt - ltd)              # shape: (1, N^2, N^2)\n", "D = lt + ebr * (ltz - ltdz)            # shape: (n, N^2, N^2)\n", "E = ltdz - ebr * (ltz - ltdz)          # shape: (n, N^2, N^2)\n", "\n", "# 扩展 B 矩阵以匹配形状\n", "B = B.expand(n, -1, -1)  # shape: (n, N^2, N^2)\n", "\n", "# 构建 H_top 和 H_bottom，shape: (n, N^2, 2*N^2)\n", "H_top = torch.cat((A, B), dim=2)  # shape: (n, N^2, 2*N^2)\n", "H_bottom = torch.cat((D, E), dim=2)  # shape: (n, N^2, 2*N^2)\n", "\n", "# 构建 H，shape: (n, 2*N^2, 2*N^2)\n", "H = torch.cat((H_top, H_bottom), dim=1)  # shape: (n, 2*N^2, 2*N^2)\n", "\n", "# 创建一个全为 -1 的列向量，shape: (n, 2*N^2, 1)\n", "col_neg_ones = -torch.ones((n, 2*N**2, 1), device=device, dtype=torch.float16)\n", "\n", "# 创建一个全为 1 的行向量，shape: (n, 1, 2*N^2 + 1)\n", "row_ones = torch.ones((n, 1, 2*N**2 + 1), device=device, dtype=torch.float16)\n", "\n", "# 将列向量添加到 H 的最右侧，shape: (n, 2*N^2, 2*N^2 + 1)\n", "H = torch.cat((H, col_neg_ones), dim=2)\n", "\n", "# 将行向量添加到 H 的最底部，shape: (n, 2*N^2 + 1, 2*N^2 + 1)\n", "H = torch.cat((H, row_ones), dim=1)\n", "\n", "# 将右下角的交界处设置为 0\n", "H[:, -1, -1] = 0\n", "\n", "print(H)\n", "print(H.shape)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["tensor([[ 362.2500,  377.5000,  390.2500,  ...,  367.7500,  353.7500,\n", "         2500.0000],\n", "        [ 362.2500,  377.5000,  390.2500,  ...,  367.0000,  353.2500,\n", "         2500.0000],\n", "        [ 362.2500,  377.5000,  390.2500,  ...,  366.2500,  352.7500,\n", "         2500.0000],\n", "        ...,\n", "        [ 362.2500,  377.5000,  390.2500,  ...,  369.7500,  355.5000,\n", "         2500.0000],\n", "        [ 362.2500,  377.5000,  390.2500,  ...,  369.0000,  355.0000,\n", "         2500.0000],\n", "        [ 362.2500,  377.5000,  390.2500,  ...,  368.2500,  354.5000,\n", "         2500.0000]], dtype=torch.float16)\n", "torch.Size([80, 5001])\n"]}], "source": ["# 计算矩阵 C，shape: (1, N^2, N^2)\n", "C = ltd - ebr * (lt - ltd)  # shape: (1, N^2, N^2)\n", "\n", "# 计算矩阵 F，shape: (n, N^2, N^2)\n", "F = ltz + ebr * (ltz - ltdz)  # shape: (n, N^2, N^2)\n", "\n", "# 创建 sigmat，shape: (N^2,)\n", "sigmat = torch.full((N**2,), rt, device=device, dtype=torch.float16)\n", "\n", "# 计算 C_sigmat，shape: (N^2,)\n", "C_sigmat = torch.matmul(<PERSON><PERSON>squeeze(0), sigmat)  # shape: (N^2,)\n", "\n", "# 计算 F_sigmat，shape: (n, N^2)\n", "F_sigmat = torch.matmul(F, sigmat)  # shape: (n, N^2)\n", "\n", "# 构建 result_vector，shape: (n, 2*N^2 + 1)\n", "result_vector = torch.cat((\n", "    C_sigmat.unsqueeze(0).expand(n, -1),  # 扩展到 (n, N^2)\n", "    F_sigmat,                             # (n, N^2)\n", "    torch.full((n, 1), q, device=device, dtype=torch.float16)  # (n, 1)\n", "), dim=1)\n", "\n", "print(result_vector)\n", "print(result_vector.shape)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["tensor([[0.3027, 0.3027, 0.3027,  ..., 0.6973, 0.6973, 1.5850],\n", "        [0.2935, 0.2935, 0.2935,  ..., 0.7065, 0.7065, 1.6055],\n", "        [0.2852, 0.2852, 0.2852,  ..., 0.7153, 0.7153, 1.6260],\n", "        ...,\n", "        [0.3342, 0.3342, 0.3342,  ..., 0.6660, 0.6660, 1.5137],\n", "        [0.3232, 0.3232, 0.3232,  ..., 0.6768, 0.6768, 1.5391],\n", "        [0.3123, 0.3123, 0.3123,  ..., 0.6880, 0.6880, 1.5625]],\n", "       dtype=torch.float16)\n", "torch.Size([80, 5001])\n"]}, {"data": {"text/plain": ["tensor([[0.3027, 0.3027, 0.3027,  ..., 0.6973, 0.6973, 1.5850],\n", "        [0.2935, 0.2935, 0.2935,  ..., 0.7065, 0.7065, 1.6055],\n", "        [0.2852, 0.2852, 0.2852,  ..., 0.7153, 0.7153, 1.6260],\n", "        ...,\n", "        [0.3342, 0.3342, 0.3342,  ..., 0.6660, 0.6660, 1.5137],\n", "        [0.3232, 0.3232, 0.3232,  ..., 0.6768, 0.6768, 1.5391],\n", "        [0.3123, 0.3123, 0.3123,  ..., 0.6880, 0.6880, 1.5625]],\n", "       dtype=torch.float16, requires_grad=True)"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["# 初始化 xt，shape: (n, 2*N^2 + 1)\n", "# 计算初始 xt\n", "xt1 = (rt * d / (ebsenr * z_values.unsqueeze(1) + d)).expand(-1, N**2)    # 形状：(n, N**2)\n", "xt2 = (ebsenr * rt * z_values.unsqueeze(1) / (ebsenr * z_values.unsqueeze(1) + d)).expand(-1, N**2)  # 形状：(n, N**2)\n", "xt3 = (rt * z_values * d / (ebsenr * z_values + d)).unsqueeze(1)          # 形状：(n, 1)\n", "\n", "# 合并 xt\n", "xt = torch.cat((xt1, xt2, xt3), dim=1)  # 形状：(n, 2*N**2 + 1)\n", "print(xt)\n", "print(xt.shape)\n", "xt = xt.to(device=device, dtype=torch.float16)\n", "xt.requires_grad_(True)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["# 检查 xt\n", "if torch.isnan(xt).any() or torch.isinf(xt).any():\n", "    print(\"xt contains NaN or Inf\")\n", "# 检查 H\n", "if torch.isnan(H).any() or torch.isinf(H).any():\n", "    print(\"H contains NaN or Inf\")\n", "# 检查 result_vector\n", "if torch.isnan(result_vector).any() or torch.isinf(result_vector).any():\n", "    print(\"result_vector contains NaN or Inf\")\n"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Epoch [10/20000], Loss: 7.6367\n", "Epoch [20/20000], Loss: 5.8086\n", "Epoch [30/20000], Loss: 4.8047\n", "Epoch [40/20000], Loss: 4.2617\n", "Epoch [50/20000], Loss: 4.1836\n", "Epoch [60/20000], Loss: 4.0234\n", "Epoch [70/20000], Loss: 3.9668\n", "Epoch [80/20000], Loss: 4.2656\n", "Epoch [90/20000], Loss: 3.7168\n", "Epoch [100/20000], Loss: 3.7324\n", "Epoch [110/20000], Loss: 3.6348\n", "Epoch [120/20000], Loss: 3.5840\n", "Epoch [130/20000], Loss: 4.0586\n", "Epoch [140/20000], Loss: 3.4238\n", "Epoch [150/20000], Loss: 3.8105\n", "Epoch [160/20000], Loss: 3.3828\n", "Epoch [170/20000], Loss: 3.3145\n", "Epoch [180/20000], Loss: 3.3379\n", "Epoch [190/20000], Loss: 3.3027\n", "Epoch [200/20000], Loss: 3.7598\n", "Epoch [210/20000], Loss: 3.6699\n", "Epoch [220/20000], Loss: 3.0449\n", "Epoch [230/20000], Loss: 3.1250\n", "Epoch [240/20000], Loss: 2.7969\n", "Epoch [250/20000], Loss: 2.7188\n", "Epoch [260/20000], Loss: 2.5039\n", "Epoch [270/20000], Loss: 2.3516\n", "Epoch [280/20000], Loss: 2.3770\n", "Epoch [290/20000], Loss: 2.2656\n", "Epoch [300/20000], Loss: 2.0508\n", "Epoch [310/20000], Loss: 2.3281\n", "Epoch [320/20000], Loss: 2.1484\n", "Epoch [330/20000], Loss: 1.8867\n", "Epoch [340/20000], Loss: 1.9180\n", "Epoch [350/20000], Loss: 3.0449\n", "Epoch [360/20000], Loss: 2.1289\n", "Epoch [370/20000], Loss: 2.0547\n", "Epoch [380/20000], Loss: 2.1270\n", "Epoch [390/20000], Loss: 2.3066\n", "Epoch [400/20000], Loss: 2.2871\n", "Epoch [410/20000], Loss: 2.9414\n", "Epoch [420/20000], Loss: 1.9277\n", "Epoch [430/20000], Loss: 2.3027\n", "Epoch [440/20000], Loss: 2.6602\n", "Epoch [450/20000], Loss: 2.8789\n", "Epoch [460/20000], Loss: 2.2715\n", "Epoch [470/20000], Loss: 1.7266\n", "Epoch [480/20000], Loss: 2.4023\n", "Epoch [490/20000], Loss: 1.5674\n", "Epoch [500/20000], Loss: 1.7949\n", "Epoch [510/20000], Loss: 1.4854\n", "Epoch [520/20000], Loss: 1.0713\n", "Epoch [530/20000], Loss: 2.2812\n", "Epoch [540/20000], Loss: 1.3057\n", "Epoch [550/20000], Loss: 1.4160\n", "Epoch [560/20000], Loss: 2.2070\n", "Epoch [570/20000], Loss: 1.5557\n", "Epoch [580/20000], Loss: 1.3066\n", "Epoch [590/20000], Loss: 1.1855\n", "Epoch [600/20000], Loss: 1.6963\n", "Epoch [610/20000], Loss: 1.0762\n", "Epoch [620/20000], Loss: 0.9077\n", "Epoch [630/20000], Loss: 2.5625\n", "Epoch [640/20000], Loss: 2.7402\n", "Epoch [650/20000], Loss: 1.9424\n", "Epoch [660/20000], Loss: 1.4883\n", "Epoch [670/20000], Loss: 2.2051\n", "Epoch [680/20000], Loss: 2.2285\n", "Epoch [690/20000], Loss: 2.1035\n", "Epoch [700/20000], Loss: 1.0889\n", "Epoch [710/20000], Loss: 0.7837\n", "Epoch [720/20000], Loss: 1.2715\n", "Epoch [730/20000], Loss: 0.9121\n", "Epoch [740/20000], Loss: 0.6875\n", "Epoch [750/20000], Loss: 0.6245\n", "Epoch [760/20000], Loss: 0.3906\n", "Epoch [770/20000], Loss: 2.0977\n", "Epoch [780/20000], Loss: 1.0752\n", "Epoch [790/20000], Loss: 0.8291\n", "Epoch [800/20000], Loss: 0.6113\n", "Epoch [810/20000], Loss: 0.5850\n", "Epoch [820/20000], Loss: 0.3171\n", "Epoch [830/20000], Loss: 0.2969\n", "Epoch [840/20000], Loss: 0.3247\n", "Epoch [850/20000], Loss: 0.2268\n", "Epoch [860/20000], Loss: 1.4541\n", "Epoch [870/20000], Loss: 1.2744\n", "Epoch [880/20000], Loss: 1.0117\n", "Epoch [890/20000], Loss: 1.4668\n", "Epoch [900/20000], Loss: 2.0625\n", "Epoch [910/20000], Loss: 0.8359\n", "Epoch [920/20000], Loss: 2.3652\n", "Epoch [930/20000], Loss: 1.5957\n", "Epoch [940/20000], Loss: 1.2510\n", "Epoch [950/20000], Loss: 0.9609\n", "Epoch [960/20000], Loss: 1.3652\n", "Epoch [970/20000], Loss: 1.1152\n", "Epoch [980/20000], Loss: 1.9131\n", "Epoch [990/20000], Loss: 1.3037\n", "Epoch [1000/20000], Loss: 0.5566\n"]}, {"ename": "KeyboardInterrupt", "evalue": "", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mKeyboardInterrupt\u001b[0m                         <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[10], line 16\u001b[0m\n\u001b[1;32m     13\u001b[0m H_batch \u001b[38;5;241m=\u001b[39m H[:, indices, :]  \u001b[38;5;66;03m# shape: (n, batch_size, 2*N**2 + 1)\u001b[39;00m\n\u001b[1;32m     15\u001b[0m \u001b[38;5;66;03m# 计算损失和梯度\u001b[39;00m\n\u001b[0;32m---> 16\u001b[0m H_xt \u001b[38;5;241m=\u001b[39m \u001b[43mtorch\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mbmm\u001b[49m\u001b[43m(\u001b[49m\u001b[43mH_batch\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mxt\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43munsqueeze\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m2\u001b[39;49m\u001b[43m)\u001b[49m\u001b[43m)\u001b[49m\u001b[38;5;241m.\u001b[39msqueeze(\u001b[38;5;241m2\u001b[39m)  \u001b[38;5;66;03m# shape: (n, batch_size)\u001b[39;00m\n\u001b[1;32m     18\u001b[0m \u001b[38;5;66;03m# 调整 result_vector 以匹配批量大小\u001b[39;00m\n\u001b[1;32m     19\u001b[0m result_batch \u001b[38;5;241m=\u001b[39m result_vector[:, indices]  \u001b[38;5;66;03m# shape: (n, batch_size)\u001b[39;00m\n", "\u001b[0;31mKeyboardInterrupt\u001b[0m: "]}], "source": ["# 迭代优化\n", "# 设置批量大小\n", "# 定义优化器\n", "optimizer = torch.optim.Adam([xt], lr=0.001)\n", "batch_size = 1000  # 根据实际情况调整\n", "\n", "for epoch in range(20000):\n", "    optimizer.zero_grad()\n", "    # 随机选择批量索引\n", "    indices = torch.randperm(H.shape[1])[:batch_size].to(device)\n", "    \n", "    # 选择小批量样本\n", "    H_batch = H[:, indices, :]  # shape: (n, batch_size, 2*N**2 + 1)\n", "    \n", "    # 计算损失和梯度\n", "    H_xt = torch.bmm(H_batch, xt.unsqueeze(2)).squeeze(2)  # shape: (n, batch_size)\n", "    \n", "    # 调整 result_vector 以匹配批量大小\n", "    result_batch = result_vector[:, indices]  # shape: (n, batch_size)\n", "    \n", "    loss = 0.5 * torch.norm(H_xt - result_batch, dim=1).mean()\n", "    loss.backward()\n", "    \n", "    # 更新参数\n", "    optimizer.step()\n", "    \n", "    if (epoch+1) % 10 == 0:\n", "        print(f'Epoch [{epoch+1}/20000], Loss: {loss.item():.4f}')\n", "    if loss.item() < 0.001:\n", "        break\n"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["tensor([ 755.5000,  733.0000,  711.0000,  691.0000,  672.5000,  657.5000,\n", "         640.5000,  628.0000,  615.0000,  602.5000,  592.5000,  584.0000,\n", "         575.0000,  569.0000,  563.0000,  559.0000,  555.0000,  553.0000,\n", "         551.5000,  550.5000,  551.5000,  553.0000,  555.0000,  559.0000,\n", "         563.0000,  569.0000,  575.0000,  584.0000,  592.5000,  602.5000,\n", "         614.5000,  626.5000,  640.5000,  656.5000,  672.5000,  691.0000,\n", "         711.0000,  733.0000,  755.0000,  781.0000,  806.5000,  836.5000,\n", "         867.0000,  897.0000,  931.0000,  966.5000, 1003.0000, 1040.0000,\n", "        1078.0000, 1116.0000, 1154.0000, 1193.0000, 1233.0000, 1268.0000,\n", "        1300.0000, 1331.0000, 1350.0000, 1370.0000, 1381.0000, 1386.0000,\n", "        1381.0000, 1368.0000, 1354.0000, 1331.0000, 1301.0000, 1266.0000,\n", "        1232.0000, 1193.0000, 1155.0000, 1115.0000, 1077.0000, 1039.0000,\n", "        1002.0000,  965.5000,  929.5000,  897.0000,  867.0000,  835.0000,\n", "         807.5000,  779.5000], dtype=torch.float16, grad_fn=<SumBackward1>)\n", "[12088. 11728. 11376. 11056. 10760. 10520. 10248. 10048.  9840.  9640.\n", "  9480.  9344.  9200.  9104.  9008.  8944.  8880.  8848.  8824.  8808.\n", "  8824.  8848.  8880.  8944.  9008.  9104.  9200.  9344.  9480.  9640.\n", "  9832. 10024. 10248. 10504. 10760. 11056. 11376. 11728. 12080. 12496.\n", " 12904. 13384. 13872. 14352. 14896. 15464. 16048. 16640. 17248. 17856.\n", " 18464. 19088. 19728. 20288. 20800. 21296. 21600. 21920. 22096. 22176.\n", " 22096. 21888. 21664. 21296. 20816. 20256. 19712. 19088. 18480. 17840.\n", " 17232. 16624. 16032. 15448. 14872. 14352. 13872. 13360. 12920. 12472.]\n"]}], "source": ["# 计算 xt 的前 N^2 个元素的和，shape: (n,)\n", "sum_xt = torch.sum(xt[:, :N**2], dim=1)\n", "# 将结果转换为 numpy 数组\n", "results = b**2* sum_xt.detach().cpu().numpy()\n", "print(sum_xt)\n", "print(results)\n", "# 将 NumPy 数组转换为 Pandas DataFrame\n", "df = pd.DataFrame(results)\n", "# 将 DataFrame 保存为 Excel 文件\n", "df.to_excel('results.xlsx', index=False)"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_890593/3868779983.py:5: FutureWarning: \n", "\n", "The `ci` parameter is deprecated. Use `errorbar=None` for the same effect.\n", "\n", "  sns.lineplot(x=range(1, n+1), y=results, marker='o', ci=None)\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1000x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# 设置Seaborn样式\n", "sns.set_theme(style=\"whitegrid\")\n", "# 绘制sum_xt随时间变化的图像\n", "plt.figure(figsize=(10, 6))\n", "sns.lineplot(x=range(1, n+1), y=results, marker='o', ci=None)\n", "plt.xlabel('t', fontsize=14)\n", "plt.ylabel('charge', fontsize=14)\n", "plt.title('charge(t)', fontsize=16)\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 对n个sum_xt逐差分\n", "diff =  np.diff(results)\n", "print(diff)\n", "# 绘制差分结果\n", "plt.figure(figsize=(10, 6))\n", "sns.lineplot(x=range(1, n), y=diff, marker='o', ci=None)\n", "plt.xlabel('t', fontsize=14)\n", "plt.ylabel('i', fontsize=14)\n", "plt.title('i(t)', fontsize=16)\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["torch.Size([80, 5001])\n", "[12062.52   11704.93   11361.114  11041.687  10741.754  10497.412\n", " 10238.969  10024.8125  9821.768   9619.424   9466.339   9328.6\n", "  9189.657   9090.359   8989.29    8925.145   8864.541   8833.005\n", "  8808.2705  8792.004   8808.2705  8833.005   8864.541   8925.145\n", "  8989.29    9090.359   9189.657   9328.6     9466.339   9619.424\n", "  9819.781  10011.425  10238.969  10488.531  10741.754  11041.687\n", " 11361.114  11704.93   12056.709  12480.769  12893.213  13373.64\n", " 13855.337  14351.729  14884.238  15450.715  16042.607  16646.271\n", " 17242.01   17866.418  18488.06   19117.102  19741.46   20306.166\n", " 20828.646  21288.441  21642.55   21940.049  22119.318  22184.64\n", " 22119.318  21945.498  21675.389  21300.45   20826.053  20293.531\n", " 19730.379  19117.102  18492.266  17865.637  17251.248  16631.605\n", " 16034.534  15449.406  14877.59   14351.727  13855.336  13361.014\n", " 12905.234  12462.578 ]\n"]}], "source": ["# 确保 H 和 result_vector 是单精度浮点数\n", "H = H.float()\n", "result_vector = result_vector.float()\n", "\n", "# 求解 Hx = result_vector\n", "x_solution = torch.linalg.solve(H, result_vector)\n", "print(x_solution.shape)\n", "# 计算 x_solution 每个维度前 N^2 个元素的和(shape: (n,))\n", "# 计算 xt 的前 N^2 个元素的和，shape: (n,)\n", "x_solution_sum = torch.sum(x_solution[:, :N**2],dim=1)\n", "results_solution = b**2 * x_solution_sum.detach().cpu().numpy()\n", "\n", "print(results_solution)\n", "\n", "# 将 NumPy 数组转换为 Pandas DataFrame\n", "df = pd.DataFrame(results_solution)\n", "# 将 DataFrame 保存为 Excel 文件\n", "df.to_excel('results_solution.xlsx', index=False)\n"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_890593/3847134001.py:3: FutureWarning: \n", "\n", "The `ci` parameter is deprecated. Use `errorbar=None` for the same effect.\n", "\n", "  sns.lineplot(x=range(1, n), y=results_solution, marker='o', ci=None)\n"]}, {"ename": "ValueError", "evalue": "All arrays must be of the same length", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[14], line 3\u001b[0m\n\u001b[1;32m      1\u001b[0m \u001b[38;5;66;03m# 绘制results_solution随时间变化的图像\u001b[39;00m\n\u001b[1;32m      2\u001b[0m plt\u001b[38;5;241m.\u001b[39mfigure(figsize\u001b[38;5;241m=\u001b[39m(\u001b[38;5;241m10\u001b[39m, \u001b[38;5;241m6\u001b[39m))\n\u001b[0;32m----> 3\u001b[0m \u001b[43msns\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mlineplot\u001b[49m\u001b[43m(\u001b[49m\u001b[43mx\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mrange\u001b[39;49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m1\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mn\u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43my\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mresults_solution\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mmarker\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43mo\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mci\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mNone\u001b[39;49;00m\u001b[43m)\u001b[49m\n\u001b[1;32m      4\u001b[0m plt\u001b[38;5;241m.\u001b[39mxlabel(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mt\u001b[39m\u001b[38;5;124m'\u001b[39m, fontsize\u001b[38;5;241m=\u001b[39m\u001b[38;5;241m14\u001b[39m)\n\u001b[1;32m      5\u001b[0m plt\u001b[38;5;241m.\u001b[39mylabel(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mcharge_precise\u001b[39m\u001b[38;5;124m'\u001b[39m, fontsize\u001b[38;5;241m=\u001b[39m\u001b[38;5;241m14\u001b[39m)\n", "File \u001b[0;32m~/.local/lib/python3.8/site-packages/seaborn/relational.py:485\u001b[0m, in \u001b[0;36mlineplot\u001b[0;34m(data, x, y, hue, size, style, units, weights, palette, hue_order, hue_norm, sizes, size_order, size_norm, dashes, markers, style_order, estimator, errorbar, n_boot, seed, orient, sort, err_style, err_kws, legend, ci, ax, **kwargs)\u001b[0m\n\u001b[1;32m    471\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mlineplot\u001b[39m(\n\u001b[1;32m    472\u001b[0m     data\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m, \u001b[38;5;241m*\u001b[39m,\n\u001b[1;32m    473\u001b[0m     x\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m, y\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m, hue\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m, size\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m, style\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m, units\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m, weights\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m,\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    481\u001b[0m \n\u001b[1;32m    482\u001b[0m     \u001b[38;5;66;03m# Handle deprecation of ci parameter\u001b[39;00m\n\u001b[1;32m    483\u001b[0m     errorbar \u001b[38;5;241m=\u001b[39m _deprecate_ci(errorbar, ci)\n\u001b[0;32m--> 485\u001b[0m     p \u001b[38;5;241m=\u001b[39m \u001b[43m_LinePlotter\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    486\u001b[0m \u001b[43m        \u001b[49m\u001b[43mdata\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mdata\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    487\u001b[0m \u001b[43m        \u001b[49m\u001b[43mvariables\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mdict\u001b[39;49m\u001b[43m(\u001b[49m\n\u001b[1;32m    488\u001b[0m \u001b[43m            \u001b[49m\u001b[43mx\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mx\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43my\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43my\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mhue\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mhue\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43msize\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43msize\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mstyle\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mstyle\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43munits\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43munits\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mweight\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mweights\u001b[49m\n\u001b[1;32m    489\u001b[0m \u001b[43m        \u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    490\u001b[0m \u001b[43m        \u001b[49m\u001b[43mestimator\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mestimator\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mn_boot\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mn_boot\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mseed\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mseed\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43merrorbar\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43merrorbar\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    491\u001b[0m \u001b[43m        \u001b[49m\u001b[43msort\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43msort\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43morient\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43morient\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43merr_style\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43merr_style\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43merr_kws\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43merr_kws\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    492\u001b[0m \u001b[43m        \u001b[49m\u001b[43mlegend\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mlegend\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    493\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    495\u001b[0m     p\u001b[38;5;241m.\u001b[39mmap_hue(palette\u001b[38;5;241m=\u001b[39mpalette, order\u001b[38;5;241m=\u001b[39mhue_order, norm\u001b[38;5;241m=\u001b[39mhue_norm)\n\u001b[1;32m    496\u001b[0m     p\u001b[38;5;241m.\u001b[39mmap_size(sizes\u001b[38;5;241m=\u001b[39msizes, order\u001b[38;5;241m=\u001b[39msize_order, norm\u001b[38;5;241m=\u001b[39msize_norm)\n", "File \u001b[0;32m~/.local/lib/python3.8/site-packages/seaborn/relational.py:216\u001b[0m, in \u001b[0;36m_LinePlotter.__init__\u001b[0;34m(self, data, variables, estimator, n_boot, seed, errorbar, sort, orient, err_style, err_kws, legend)\u001b[0m\n\u001b[1;32m    202\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21m__init__\u001b[39m(\n\u001b[1;32m    203\u001b[0m     \u001b[38;5;28mself\u001b[39m, \u001b[38;5;241m*\u001b[39m,\n\u001b[1;32m    204\u001b[0m     data\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m, variables\u001b[38;5;241m=\u001b[39m{},\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    210\u001b[0m     \u001b[38;5;66;03m# the kind of plot to draw, but for the time being we need to set\u001b[39;00m\n\u001b[1;32m    211\u001b[0m     \u001b[38;5;66;03m# this information so the SizeMapping can use it\u001b[39;00m\n\u001b[1;32m    212\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_default_size_range \u001b[38;5;241m=\u001b[39m (\n\u001b[1;32m    213\u001b[0m         np\u001b[38;5;241m.\u001b[39mr_[\u001b[38;5;241m.5\u001b[39m, \u001b[38;5;241m2\u001b[39m] \u001b[38;5;241m*\u001b[39m mpl\u001b[38;5;241m.\u001b[39mrcParams[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mlines.linewidth\u001b[39m\u001b[38;5;124m\"\u001b[39m]\n\u001b[1;32m    214\u001b[0m     )\n\u001b[0;32m--> 216\u001b[0m     \u001b[38;5;28;43msuper\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[38;5;21;43m__init__\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43mdata\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mdata\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mvariables\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mvariables\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    218\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mestimator \u001b[38;5;241m=\u001b[39m estimator\n\u001b[1;32m    219\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39merrorbar \u001b[38;5;241m=\u001b[39m errorbar\n", "File \u001b[0;32m~/.local/lib/python3.8/site-packages/seaborn/_base.py:634\u001b[0m, in \u001b[0;36mVectorPlotter.__init__\u001b[0;34m(self, data, variables)\u001b[0m\n\u001b[1;32m    629\u001b[0m \u001b[38;5;66;03m# var_ordered is relevant only for categorical axis variables, and may\u001b[39;00m\n\u001b[1;32m    630\u001b[0m \u001b[38;5;66;03m# be better handled by an internal axis information object that tracks\u001b[39;00m\n\u001b[1;32m    631\u001b[0m \u001b[38;5;66;03m# such information and is set up by the scale_* methods. The analogous\u001b[39;00m\n\u001b[1;32m    632\u001b[0m \u001b[38;5;66;03m# information for numeric axes would be information about log scales.\u001b[39;00m\n\u001b[1;32m    633\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_var_ordered \u001b[38;5;241m=\u001b[39m {\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mx\u001b[39m\u001b[38;5;124m\"\u001b[39m: \u001b[38;5;28;01mFalse\u001b[39;00m, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124my\u001b[39m\u001b[38;5;124m\"\u001b[39m: \u001b[38;5;28;01m<PERSON>alse\u001b[39;00m}  \u001b[38;5;66;03m# alt., used DefaultDict\u001b[39;00m\n\u001b[0;32m--> 634\u001b[0m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43massign_variables\u001b[49m\u001b[43m(\u001b[49m\u001b[43mdata\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mvariables\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    636\u001b[0m \u001b[38;5;66;03m# TODO Lots of tests assume that these are called to initialize the\u001b[39;00m\n\u001b[1;32m    637\u001b[0m \u001b[38;5;66;03m# mappings to default values on class initialization. I'd prefer to\u001b[39;00m\n\u001b[1;32m    638\u001b[0m \u001b[38;5;66;03m# move away from that and only have a mapping when explicitly called.\u001b[39;00m\n\u001b[1;32m    639\u001b[0m \u001b[38;5;28;01mfor\u001b[39;00m var \u001b[38;5;129;01min\u001b[39;00m [\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mhue\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124msize\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mstyle\u001b[39m\u001b[38;5;124m\"\u001b[39m]:\n", "File \u001b[0;32m~/.local/lib/python3.8/site-packages/seaborn/_base.py:679\u001b[0m, in \u001b[0;36mVectorPlotter.assign_variables\u001b[0;34m(self, data, variables)\u001b[0m\n\u001b[1;32m    674\u001b[0m \u001b[38;5;28;01<PERSON><PERSON>\u001b[39;00m:\n\u001b[1;32m    675\u001b[0m     \u001b[38;5;66;03m# When dealing with long-form input, use the newer PlotData\u001b[39;00m\n\u001b[1;32m    676\u001b[0m     \u001b[38;5;66;03m# object (internal but introduced for the objects interface)\u001b[39;00m\n\u001b[1;32m    677\u001b[0m     \u001b[38;5;66;03m# to centralize / standardize data consumption logic.\u001b[39;00m\n\u001b[1;32m    678\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39minput_format \u001b[38;5;241m=\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mlong\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[0;32m--> 679\u001b[0m     plot_data \u001b[38;5;241m=\u001b[39m \u001b[43mPlotData\u001b[49m\u001b[43m(\u001b[49m\u001b[43mdata\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mvariables\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    680\u001b[0m     frame \u001b[38;5;241m=\u001b[39m plot_data\u001b[38;5;241m.\u001b[39mframe\n\u001b[1;32m    681\u001b[0m     names \u001b[38;5;241m=\u001b[39m plot_data\u001b[38;5;241m.\u001b[39mnames\n", "File \u001b[0;32m~/.local/lib/python3.8/site-packages/seaborn/_core/data.py:58\u001b[0m, in \u001b[0;36mPlotData.__init__\u001b[0;34m(self, data, variables)\u001b[0m\n\u001b[1;32m     51\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21m__init__\u001b[39m(\n\u001b[1;32m     52\u001b[0m     \u001b[38;5;28mself\u001b[39m,\n\u001b[1;32m     53\u001b[0m     data: DataSource,\n\u001b[1;32m     54\u001b[0m     variables: \u001b[38;5;28mdict\u001b[39m[\u001b[38;5;28mstr\u001b[39m, VariableSpec],\n\u001b[1;32m     55\u001b[0m ):\n\u001b[1;32m     57\u001b[0m     data \u001b[38;5;241m=\u001b[39m handle_data_source(data)\n\u001b[0;32m---> 58\u001b[0m     frame, names, ids \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_assign_variables\u001b[49m\u001b[43m(\u001b[49m\u001b[43mdata\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mvariables\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m     60\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mframe \u001b[38;5;241m=\u001b[39m frame\n\u001b[1;32m     61\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mnames \u001b[38;5;241m=\u001b[39m names\n", "File \u001b[0;32m~/.local/lib/python3.8/site-packages/seaborn/_core/data.py:265\u001b[0m, in \u001b[0;36mPlotData._assign_variables\u001b[0;34m(self, data, variables)\u001b[0m\n\u001b[1;32m    260\u001b[0m             ids[key] \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mid\u001b[39m(val)\n\u001b[1;32m    262\u001b[0m \u001b[38;5;66;03m# Construct a tidy plot DataFrame. This will convert a number of\u001b[39;00m\n\u001b[1;32m    263\u001b[0m \u001b[38;5;66;03m# types automatically, aligning on index in case of pandas objects\u001b[39;00m\n\u001b[1;32m    264\u001b[0m \u001b[38;5;66;03m# TODO Note: this fails when variable specs *only* have scalars!\u001b[39;00m\n\u001b[0;32m--> 265\u001b[0m frame \u001b[38;5;241m=\u001b[39m \u001b[43mpd\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mDataFrame\u001b[49m\u001b[43m(\u001b[49m\u001b[43mplot_data\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    267\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m frame, names, ids\n", "File \u001b[0;32m~/.local/lib/python3.8/site-packages/pandas/core/frame.py:709\u001b[0m, in \u001b[0;36mDataFrame.__init__\u001b[0;34m(self, data, index, columns, dtype, copy)\u001b[0m\n\u001b[1;32m    703\u001b[0m     mgr \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_init_mgr(\n\u001b[1;32m    704\u001b[0m         data, axes\u001b[38;5;241m=\u001b[39m{\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mindex\u001b[39m\u001b[38;5;124m\"\u001b[39m: index, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mcolumns\u001b[39m\u001b[38;5;124m\"\u001b[39m: columns}, dtype\u001b[38;5;241m=\u001b[39mdtype, copy\u001b[38;5;241m=\u001b[39mcopy\n\u001b[1;32m    705\u001b[0m     )\n\u001b[1;32m    707\u001b[0m \u001b[38;5;28;01melif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(data, \u001b[38;5;28mdict\u001b[39m):\n\u001b[1;32m    708\u001b[0m     \u001b[38;5;66;03m# GH#38939 de facto copy defaults to False only in non-dict cases\u001b[39;00m\n\u001b[0;32m--> 709\u001b[0m     mgr \u001b[38;5;241m=\u001b[39m \u001b[43mdict_to_mgr\u001b[49m\u001b[43m(\u001b[49m\u001b[43mdata\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mindex\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mcolumns\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mdtype\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mdtype\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mcopy\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mcopy\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mtyp\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mmanager\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    710\u001b[0m \u001b[38;5;28;01melif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(data, ma\u001b[38;5;241m.\u001b[39mMaskedArray):\n\u001b[1;32m    711\u001b[0m     \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mnumpy\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mma\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m mrecords\n", "File \u001b[0;32m~/.local/lib/python3.8/site-packages/pandas/core/internals/construction.py:481\u001b[0m, in \u001b[0;36mdict_to_mgr\u001b[0;34m(data, index, columns, dtype, typ, copy)\u001b[0m\n\u001b[1;32m    477\u001b[0m     \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m    478\u001b[0m         \u001b[38;5;66;03m# dtype check to exclude e.g. range objects, scalars\u001b[39;00m\n\u001b[1;32m    479\u001b[0m         arrays \u001b[38;5;241m=\u001b[39m [x\u001b[38;5;241m.\u001b[39mcopy() \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mhasattr\u001b[39m(x, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mdtype\u001b[39m\u001b[38;5;124m\"\u001b[39m) \u001b[38;5;28;01melse\u001b[39;00m x \u001b[38;5;28;01mfor\u001b[39;00m x \u001b[38;5;129;01min\u001b[39;00m arrays]\n\u001b[0;32m--> 481\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43marrays_to_mgr\u001b[49m\u001b[43m(\u001b[49m\u001b[43marrays\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mcolumns\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mindex\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mdtype\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mdtype\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mtyp\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mtyp\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mconsolidate\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mcopy\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/.local/lib/python3.8/site-packages/pandas/core/internals/construction.py:115\u001b[0m, in \u001b[0;36marrays_to_mgr\u001b[0;34m(arrays, columns, index, dtype, verify_integrity, typ, consolidate)\u001b[0m\n\u001b[1;32m    112\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m verify_integrity:\n\u001b[1;32m    113\u001b[0m     \u001b[38;5;66;03m# figure out the index, if necessary\u001b[39;00m\n\u001b[1;32m    114\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m index \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[0;32m--> 115\u001b[0m         index \u001b[38;5;241m=\u001b[39m \u001b[43m_extract_index\u001b[49m\u001b[43m(\u001b[49m\u001b[43marrays\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    116\u001b[0m     \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[1;32m    117\u001b[0m         index \u001b[38;5;241m=\u001b[39m ensure_index(index)\n", "File \u001b[0;32m~/.local/lib/python3.8/site-packages/pandas/core/internals/construction.py:655\u001b[0m, in \u001b[0;36m_extract_index\u001b[0;34m(data)\u001b[0m\n\u001b[1;32m    653\u001b[0m lengths \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mlist\u001b[39m(\u001b[38;5;28mset\u001b[39m(raw_lengths))\n\u001b[1;32m    654\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mlen\u001b[39m(lengths) \u001b[38;5;241m>\u001b[39m \u001b[38;5;241m1\u001b[39m:\n\u001b[0;32m--> 655\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mValueError\u001b[39;00m(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mAll arrays must be of the same length\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m    657\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m have_dicts:\n\u001b[1;32m    658\u001b[0m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mValueError\u001b[39;00m(\n\u001b[1;32m    659\u001b[0m         \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mMixing dicts with non-Series may lead to ambiguous ordering.\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m    660\u001b[0m     )\n", "\u001b[0;31mValueError\u001b[0m: All arrays must be of the same length"]}, {"data": {"text/plain": ["<Figure size 1000x600 with 0 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# 绘制results_solution随时间变化的图像\n", "plt.figure(figsize=(10, 6))\n", "sns.lineplot(x=range(1, n), y=results_solution, marker='o')\n", "plt.xlabel('t', fontsize=14)\n", "plt.ylabel('charge_precise', fontsize=14)\n", "plt.title('charge_precise(t)', fontsize=16)\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# 绘制两个结果的对比\n", "plt.figure(figsize=(10, 6))\n", "sns.lineplot(x=range(1, n), y=results, marker='o', label='xt的和')\n", "sns.lineplot(x=range(1, n), y=results_solution, marker='o', label='xt的和的解')\n", "plt.xlabel('t', fontsize=14)\n", "plt.ylabel('charge', fontsize=14)\n", "plt.title('xt的和与解的对比', fontsize=16)\n", "plt.legend()\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# 计算sum_xt和results_solution的误差和相对误差\n", "error = results - results_solution\n", "relative_error = error / results_solution\n", "error_mean = np.mean(error)\n", "relative_error_mean = np.mean(relative_error)\n", "print(f'误差的平均值：{error_mean:.4f}')\n", "print(f'相对误差的平均值：{relative_error_mean:.4f}')\n", "\n", "plt.figure(figsize=(10, 6))\n", "sns.lineplot(x=range(1, n), y=relative_error, marker='o', ci=None)\n", "plt.xlabel('t', fontsize=14)\n", "plt.ylabel('relative error', fontsize=14)\n", "plt.title('relative error', fontsize=16)\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.18"}}, "nbformat": 4, "nbformat_minor": 4}