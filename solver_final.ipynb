{"cells": [{"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["import torch\n", "import numpy as np\n", "import seaborn as sns\n", "import matplotlib.pyplot as plt\n", "from math import sin,pi"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# 检查是否有可用的 GPU\n", "device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["rt = 1.2\n", "d = 0.5\n", "l= 200\n", "z = 1+0.3*sin(2*pi/8000)\n", "ebsenr = 2.2\n", "ebr = torch.tensor(-(ebsenr - 1) / (ebsenr * 2), device=device, dtype=torch.float32)  # 直接在 GPU 上定义 ebr"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["def block(N,q,xt,b,lr):\n", "    i, j = np.meshgrid(np.arange(1, N+1), np.arange(1, N+1), indexing='ij')\n", "    x = (i - 1/2).flatten() * b\n", "    y = (j - 1/2).flatten() * b\n", "    # 将坐标转换为张量并移动到 GPU\n", "    x = torch.tensor(x, device=device, dtype=torch.float32)\n", "    y = torch.tensor(y, device=device, dtype=torch.float32)\n", "    # 将 d 和 z 转换为张量\n", "    d_tensor = torch.tensor(d, device=device, dtype=torch.float32)\n", "    z_tensor = torch.tensor(z, device=device, dtype=torch.float32)\n", "\n", "    # 计算距离矩阵\n", "    dist = torch.sqrt((x[:, None] - x[None, :])**2 + (y[:, None] - y[None, :])**2)\n", "    dist_d = torch.sqrt(dist**2 + d_tensor**2)\n", "    dist_z = torch.sqrt(dist**2 + z_tensor**2)\n", "    dist_dz = torch.sqrt(dist**2 + (d_tensor + z_tensor)**2)\n", "\n", "    # 创建矩阵\n", "    lt = torch.where(dist == 0, 4 * b * 0.8814, b**2 / dist)\n", "    ltd = torch.where(dist_d < b, 2 * np.pi * (b / 2) * (-d_tensor / (b / 2) + torch.sqrt((d_tensor / (b / 2))**2 + 4 / np.pi)), b**2 / dist_d)\n", "    ltz = torch.where(dist_z < b, 2 * np.pi * (b / 2) * (-z_tensor / (b / 2) + torch.sqrt((z_tensor / (b / 2))**2 + 4 / np.pi)), b**2 / dist_z)\n", "    ltdz = torch.where(dist_dz < b, 2 * np.pi * (b / 2) * (-(d_tensor+z_tensor) / (b / 2) + torch.sqrt(((d_tensor+z_tensor) / (b / 2))**2 + 4 / np.pi)), b**2 / dist_dz)\n", "\n", "    A = ltdz - ebr * (lt - ltd)\n", "    B = lt + ebr * (lt - ltd)\n", "    D = lt + ebr * (ltz - ltdz)\n", "    E = ltdz - ebr * (ltz - ltdz)\n", "    # 将矩阵合成为一个矩阵\n", "    H_top = torch.cat((A, B), dim=1)\n", "    H_bottom = torch.cat((D, E), dim=1)\n", "    H = torch.cat((H_top, H_bottom), dim=0)\n", "    # 创建一个全为 -1 的列向量\n", "    col_neg_ones = -torch.ones((H.shape[0], 1), device=device, dtype=torch.float32)\n", "    # 创建一个全为 1 的行向量\n", "    row_ones = torch.ones((1, H.shape[1] + 1), device=device, dtype=torch.float32)\n", "    # 将列向量添加到矩阵 H 的最右侧\n", "    H = torch.hstack((H, col_neg_ones))\n", "    # 将行向量添加到矩阵 H 的最底部\n", "    H = torch.vstack((H, row_ones))\n", "    # 将右下角的交界处设置为 0\n", "    H[-1, -1] = 0\n", "\n", "    C = ltd - ebr * (lt - ltd)\n", "    F = ltz + ebr * (ltz - ltdz)\n", "    sigmat = torch.full((N**2,), rt, device=device, dtype=torch.float32)  # 确保 sigmat 已经正确初始化\n", "    # 计算矩阵 C 和 F 乘以向量 sigmat\n", "    C_sigmat = torch.matmul(C, sigmat)\n", "    F_sigmat = torch.matmul(F, sigmat)\n", "    # 合并结果向量，并在最后添加一个元素 10000\n", "    result_vector = torch.cat((C_sigmat, F_sigmat, torch.tensor([q], device=device, dtype=torch.float32)))\n", "\n", "\n", "    # 定义优化器\n", "    optimizer = torch.optim.Adam([xt], lr=lr)\n", "    batch_size = int(N**2 / 10 ) # 设置小批量的大小\n", "    total_size = H.shape[0]\n", "    # 迭代优化\n", "    for epoch in range(20000):\n", "        optimizer.zero_grad()\n", "        # 随机选择部分样本数据\n", "        indices = torch.randint(0, total_size, (batch_size,), device=device)\n", "        H_batch = H[indices]\n", "        result_vector_batch = result_vector[indices]\n", "        # 计算损失和梯度\n", "        loss = 0.5 * torch.norm(torch.matmul(H_batch, xt) - result_vector_batch) ** 2\n", "        loss.backward()\n", "        optimizer.step()\n", "        if (epoch+1) % 200 == 0:\n", "            print(f'Epoch [{epoch+1}/20000], Loss: {loss.item():.4f}')\n", "        if loss.item()<0.0002:\n", "            break\n", "    return xt"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["def transform_tensor(xt, N):\n", "    # 提取前 2*N**2 个元素\n", "    xt_extracted = xt[:2*N**2]\n", "    \n", "    # 将每个元素重复一次\n", "    xt_repeated = xt_extracted.repeat_interleave(2)\n", "    # 每2*N个元素重复一次\n", "    xt_repeated_2n = xt_repeated.view(-1, 2 * N).repeat(1, 2).view(-1)\n", "    # 加上原 xt 的最后一个元素\n", "    xt_final = torch.cat((xt_repeated_2n, xt[-1].view(1)), dim=0)\n", "    \n", "    return xt_final"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["tensor([0.2222, 0.2222, 0.2222,  ..., 0.9778, 0.9778, 0.2222], device='cuda:0',\n", "       requires_grad=True)\n", "Epoch [200/20000], Loss: 0.0075\n", "Epoch [400/20000], Loss: 0.0123\n", "Epoch [600/20000], Loss: 0.0074\n", "Epoch [800/20000], Loss: 0.0056\n", "Epoch [1000/20000], Loss: 0.0052\n", "Epoch [1200/20000], Loss: 0.0186\n", "Epoch [1400/20000], Loss: 0.0045\n", "Epoch [1600/20000], Loss: 0.0069\n", "Epoch [1800/20000], Loss: 0.0068\n", "Epoch [2000/20000], Loss: 0.0040\n", "Epoch [2200/20000], Loss: 0.0117\n", "Epoch [2400/20000], Loss: 0.0053\n", "Epoch [2600/20000], Loss: 0.0123\n", "Epoch [2800/20000], Loss: 0.0006\n", "Epoch [3000/20000], Loss: 0.0014\n", "Epoch [3200/20000], Loss: 0.0244\n", "Epoch [3400/20000], Loss: 0.0020\n", "Epoch [3600/20000], Loss: 0.0018\n", "Epoch [3800/20000], Loss: 0.0011\n", "Epoch [4000/20000], Loss: 0.0012\n", "Epoch [4200/20000], Loss: 0.0055\n", "Epoch [4400/20000], Loss: 0.0034\n", "Epoch [4600/20000], Loss: 0.0097\n", "Epoch [4800/20000], Loss: 0.0032\n", "Epoch [5000/20000], Loss: 0.0004\n", "Epoch [5200/20000], Loss: 0.0025\n", "Epoch [5400/20000], Loss: 0.0033\n", "Epoch [5600/20000], Loss: 0.0011\n", "Epoch [5800/20000], Loss: 0.0006\n", "Epoch [6000/20000], Loss: 0.0013\n", "Epoch [6200/20000], Loss: 0.0047\n", "Epoch [6400/20000], Loss: 0.0243\n", "Epoch [6600/20000], Loss: 0.0011\n", "Epoch [6800/20000], Loss: 0.0041\n", "Epoch [7000/20000], Loss: 0.0008\n", "Epoch [7200/20000], Loss: 0.0021\n", "tensor([0.2194, 0.2201, 0.2199,  ..., 0.9804, 0.9797, 0.1921], device='cuda:0',\n", "       requires_grad=True)\n", "tensor([0.2194, 0.2194, 0.2201,  ..., 0.9797, 0.9797, 0.1921], device='cuda:0',\n", "       requires_grad=True)\n"]}], "source": ["# first block\n", "N=25\n", "b=l/N\n", "q = rt*N**2 \n", "lr = 0.0002\n", "# 初始化待求解的变量 xt，并设置 requires_grad=True\n", "xt = torch.cat((\n", "    torch.full((N**2,), rt * d / (ebsenr * z + d), device=device, dtype=torch.float32),\n", "    torch.full((N**2,), ebsenr * rt * z / (ebsenr * z + d), device=device, dtype=torch.float32),\n", "    torch.tensor([rt * z * d / (ebsenr * z + d)], device=device, dtype=torch.float32)), dim=0)\n", "xt.requires_grad_(True)\n", "print(xt)\n", "\n", "xt = block(N,q,xt,b,lr).detach().clone().requires_grad_(True).to(device)\n", "print(xt)\n", "\n", "xt = transform_tensor(xt,N).detach().clone().requires_grad_(True).to(device)\n", "print(xt)\n"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Epoch [200/20000], Loss: 0.0326\n", "Epoch [400/20000], Loss: 0.0231\n", "Epoch [600/20000], Loss: 0.0217\n", "Epoch [800/20000], Loss: 0.0246\n", "Epoch [1000/20000], Loss: 0.1211\n", "Epoch [1200/20000], Loss: 0.0085\n", "Epoch [1400/20000], Loss: 0.0073\n", "Epoch [1600/20000], Loss: 0.0061\n", "Epoch [1800/20000], Loss: 0.0069\n", "Epoch [2000/20000], Loss: 0.0044\n", "Epoch [2200/20000], Loss: 0.0054\n", "Epoch [2400/20000], Loss: 0.0027\n", "Epoch [2600/20000], Loss: 0.0019\n", "Epoch [2800/20000], Loss: 0.0016\n", "Epoch [3000/20000], Loss: 0.0029\n", "Epoch [3200/20000], Loss: 0.0016\n", "Epoch [3400/20000], Loss: 0.0008\n", "Epoch [3600/20000], Loss: 0.0008\n", "Epoch [3800/20000], Loss: 0.0044\n", "Epoch [4000/20000], Loss: 0.0004\n", "Epoch [4200/20000], Loss: 0.0712\n", "Epoch [4400/20000], Loss: 0.0120\n", "Epoch [4600/20000], Loss: 0.0023\n", "tensor([0.2173, 0.2206, 0.2202,  ..., 0.9765, 0.9729, 0.1660], device='cuda:0',\n", "       requires_grad=True)\n", "tensor([0.2173, 0.2173, 0.2206,  ..., 0.9729, 0.9729, 0.1660], device='cuda:0',\n", "       requires_grad=True)\n"]}], "source": ["# second block\n", "N=50\n", "b=l/N\n", "lr = 0.0001\n", "q = rt*N**2\n", "xt = block(N,q,xt,b,lr).detach().clone().requires_grad_(True).to(device)\n", "print(xt)\n", "xt = transform_tensor(xt,N).detach().clone().requires_grad_(True).to(device)\n", "print(xt)\n"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Epoch [200/20000], Loss: 0.2211\n", "Epoch [400/20000], Loss: 0.2526\n", "Epoch [600/20000], Loss: 0.2009\n", "Epoch [800/20000], Loss: 0.6306\n", "Epoch [1000/20000], Loss: 0.1758\n", "Epoch [1200/20000], Loss: 0.1702\n", "Epoch [1400/20000], Loss: 0.1591\n", "Epoch [1600/20000], Loss: 0.1541\n", "Epoch [1800/20000], Loss: 0.1680\n", "Epoch [2000/20000], Loss: 0.1276\n", "Epoch [2200/20000], Loss: 0.1275\n"]}, {"ename": "RuntimeError", "evalue": "CUDA error: unknown error\nCUDA kernel errors might be asynchronously reported at some other API call, so the stacktrace below might be incorrect.\nFor debugging consider passing CUDA_LAUNCH_BLOCKING=1\nCompile with `TORCH_USE_CUDA_DSA` to enable device-side assertions.\n", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mRuntimeError\u001b[0m                              <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[11], line 6\u001b[0m\n\u001b[0;32m      4\u001b[0m lr \u001b[38;5;241m=\u001b[39m \u001b[38;5;241m0.00002\u001b[39m\n\u001b[0;32m      5\u001b[0m q \u001b[38;5;241m=\u001b[39m rt\u001b[38;5;241m*\u001b[39mN\u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39m\u001b[38;5;241m2\u001b[39m\n\u001b[1;32m----> 6\u001b[0m xt \u001b[38;5;241m=\u001b[39m \u001b[43mblock\u001b[49m\u001b[43m(\u001b[49m\u001b[43mN\u001b[49m\u001b[43m,\u001b[49m\u001b[43mq\u001b[49m\u001b[43m,\u001b[49m\u001b[43mxt\u001b[49m\u001b[43m,\u001b[49m\u001b[43mb\u001b[49m\u001b[43m,\u001b[49m\u001b[43mlr\u001b[49m\u001b[43m)\u001b[49m\u001b[38;5;241m.\u001b[39mdetach()\u001b[38;5;241m.\u001b[39mclone()\u001b[38;5;241m.\u001b[39mrequires_grad_(\u001b[38;5;28;01mTrue\u001b[39;00m)\u001b[38;5;241m.\u001b[39mto(device)\n\u001b[0;32m      7\u001b[0m \u001b[38;5;28mprint\u001b[39m(xt)\n", "Cell \u001b[1;32mIn[4], line 70\u001b[0m, in \u001b[0;36mblock\u001b[1;34m(N, q, xt, b, lr)\u001b[0m\n\u001b[0;32m     68\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m (epoch\u001b[38;5;241m+\u001b[39m\u001b[38;5;241m1\u001b[39m) \u001b[38;5;241m%\u001b[39m \u001b[38;5;241m200\u001b[39m \u001b[38;5;241m==\u001b[39m \u001b[38;5;241m0\u001b[39m:\n\u001b[0;32m     69\u001b[0m         \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mEpoch [\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mepoch\u001b[38;5;241m+\u001b[39m\u001b[38;5;241m1\u001b[39m\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m/20000], Loss: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mloss\u001b[38;5;241m.\u001b[39mitem()\u001b[38;5;132;01m:\u001b[39;00m\u001b[38;5;124m.4f\u001b[39m\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m'\u001b[39m)\n\u001b[1;32m---> 70\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[43mloss\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mitem\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[38;5;241m<\u001b[39m\u001b[38;5;241m0.0002\u001b[39m:\n\u001b[0;32m     71\u001b[0m         \u001b[38;5;28;01mbreak\u001b[39;00m\n\u001b[0;32m     72\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m xt\n", "\u001b[1;31mRuntimeError\u001b[0m: CUDA error: unknown error\nCUDA kernel errors might be asynchronously reported at some other API call, so the stacktrace below might be incorrect.\nFor debugging consider passing CUDA_LAUNCH_BLOCKING=1\nCompile with `TORCH_USE_CUDA_DSA` to enable device-side assertions.\n"]}], "source": ["# third block\n", "N=100\n", "b=l/N\n", "lr = 0.00002\n", "q = rt*N**2\n", "xt = block(N,q,xt,b,lr).detach().clone().requires_grad_(True).to(device)\n", "print(xt)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "#验证\n", "def solve(N, q, b):\n", "    i, j = np.meshgrid(np.arange(1, N+1), np.arange(1, N+1), indexing='ij')\n", "    x = (i - 1/2).flatten() * b\n", "    y = (j - 1/2).flatten() * b\n", "    # 将坐标转换为张量并移动到 GPU\n", "    x = torch.tensor(x, device=device, dtype=torch.float32)\n", "    y = torch.tensor(y, device=device, dtype=torch.float32)\n", "    # 将 d 和 z 转换为张量\n", "    d_tensor = torch.tensor(d, device=device, dtype=torch.float32)\n", "    z_tensor = torch.tensor(z, device=device, dtype=torch.float32)\n", "\n", "    # 计算距离矩阵\n", "    dist = torch.sqrt((x[:, None] - x[None, :])**2 + (y[:, None] - y[None, :])**2)\n", "    dist_d = torch.sqrt(dist**2 + d_tensor**2)\n", "    dist_z = torch.sqrt(dist**2 + z_tensor**2)\n", "    dist_dz = torch.sqrt(dist**2 + (d_tensor + z_tensor)**2)\n", "\n", "    # 创建矩阵\n", "    lt = torch.where(dist == 0, 4 * b * 0.8814, b**2 / dist)\n", "    ltd = torch.where(dist_d < b, 2 * np.pi * (b / 2) * (-d_tensor / (b / 2) + torch.sqrt((d_tensor / (b / 2))**2 + 4 / np.pi)), b**2 / dist_d)\n", "    ltz = torch.where(dist_z < b, 2 * np.pi * (b / 2) * (-z_tensor / (b / 2) + torch.sqrt((z_tensor / (b / 2))**2 + 4 / np.pi)), b**2 / dist_z)\n", "    ltdz = torch.where(dist_dz < b, 2 * np.pi * (b / 2) * (-(d_tensor+z_tensor) / (b / 2) + torch.sqrt(((d_tensor+z_tensor) / (b / 2))**2 + 4 / np.pi)), b**2 / dist_dz)\n", "\n", "    A = ltdz - ebr * (lt - ltd)\n", "    B = lt + ebr * (lt - ltd)\n", "    D = lt + ebr * (ltz - ltdz)\n", "    E = ltdz - ebr * (ltz - ltdz)\n", "    # 将矩阵合成为一个矩阵\n", "    H_top = torch.cat((A, B), dim=1)\n", "    H_bottom = torch.cat((D, E), dim=1)\n", "    H = torch.cat((H_top, H_bottom), dim=0)\n", "    # 创建一个全为 -1 的列向量\n", "    col_neg_ones = -torch.ones((H.shape[0], 1), device=device, dtype=torch.float32)\n", "    # 创建一个全为 1 的行向量\n", "    row_ones = torch.ones((1, H.shape[1] + 1), device=device, dtype=torch.float32)\n", "    # 将列向量添加到矩阵 H 的最右侧\n", "    H = torch.hstack((H, col_neg_ones))\n", "    # 将行向量添加到矩阵 H 的最底部\n", "    H = torch.vstack((H, row_ones))\n", "    # 将右下角的交界处设置为 0\n", "    H[-1, -1] = 0\n", "\n", "    C = ltd - ebr * (lt - ltd)\n", "    F = ltz + ebr * (ltz - ltdz)\n", "    sigmat = torch.full((N**2,), rt, device=device, dtype=torch.float32)  # 确保 sigmat 已经正确初始化\n", "    # 计算矩阵 C 和 F 乘以向量 sigmat\n", "    C_sigmat = torch.matmul(C, sigmat)\n", "    F_sigmat = torch.matmul(F, sigmat)\n", "    # 合并结果向量，并在最后添加一个元素 10000\n", "    result_vector = torch.cat((C_sigmat, F_sigmat, torch.tensor([q], device=device, dtype=torch.float32)))\n", "   \n", "    # 求解 Hx = result_vector\n", "    x_solution = torch.linalg.solve(H, result_vector)\n", "    return x_solution\n", "N=100\n", "b=l/N\n", "q = rt*N**2\n", "x_solution = solve(N, q, b)\n", "\n", "# 将张量转换为 NumPy 数组\n", "x_numpy = x_solution.detach().cpu().numpy()\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 将张量转换为 NumPy 数组\n", "xt_numpy = xt.detach().cpu().numpy()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import seaborn as sns\n", "\n", "import matplotlib.pyplot as plt\n", "\n", "# 计算 x_solution 和 xt 的误差\n", "error = torch.norm(x_solution - xt).item()\n", "relative_error = torch.norm(x_solution - xt).item() / torch.norm(x_solution).item()\n", "print(f'Error between x_solution and xt: {error:.4f}')\n", "print(f'Realtive error between x_solution and xt: {relative_error:.4f}')\n", "\n", "# 计算 x_solution 和 xt 的相对误差\n", "relative_error = torch.abs(x_solution - xt) / x_solution\n", "print(f'Relative error between x_solution and xt: {relative_error}')\n", "# 将相对误差保存为excel文件\n", "import pandas as pd\n", "df = pd.DataFrame(relative_error.detach().cpu().numpy())\n", "df.to_excel('relative_error.xlsx', index=False)\n", "\n", "# 设置 Seaborn 样式\n", "sns.set(style=\"whitegrid\")\n", "\n", "# 绘制相对误差图\n", "plt.figure(figsize=(10, 6))\n", "sns.lineplot(data=relative_error.detach().cpu().numpy())\n", "plt.title('Relative Error between x_solution and xt')\n", "plt.xlabel('Index')\n", "plt.ylabel('Relative Error')\n", "plt.show()\n", "\n", "# 绘制误差图\n", "plt.figure(figsize=(10, 6))\n", "sns.lineplot(data=x_solution.detach().cpu().numpy(), label='x_solution')\n", "sns.lineplot(data=xt.detach().cpu().numpy(), label='xt')\n", "plt.legend()\n", "plt.title('Comparison of x_solution and xt')\n", "plt.xlabel('Index')\n", "plt.ylabel('Value')\n", "plt.show()\n", "\n", "\n", "\n"]}], "metadata": {"kernelspec": {"display_name": "xypytorch", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.19"}}, "nbformat": 4, "nbformat_minor": 4}