#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
电磁场仿真智能体演示脚本
展示三种仿真功能的完整流程
"""

import matplotlib.pyplot as plt
import numpy as np
from electromagnetic_agent import ElectromagneticAgent
import time

def print_header(title):
    """打印标题"""
    print("\n" + "="*60)
    print(f"🔬 {title}")
    print("="*60)

def print_section(title):
    """打印章节标题"""
    print(f"\n📋 {title}")
    print("-"*40)

def demo_charge_distribution():
    """演示电荷分布仿真"""
    print_section("电荷分布仿真演示")
    
    # 创建智能体
    api_key = "sk-darna2nrdmzrwbcx"
    agent = ElectromagneticAgent(api_key)
    
    # 测试用例
    test_input = "计算电荷分布，长度网格30，宽度网格30，b=1，rt=1，d=0.6，z=0.6，epsilon=2.2"
    
    print(f"输入: {test_input}")
    print("🤖 智能体正在分析...")
    
    start_time = time.time()
    response = agent.process_request(test_input)
    end_time = time.time()
    
    if response['success']:
        print(f"✅ 仿真成功完成！(耗时: {end_time-start_time:.2f}秒)")
        print(f"📊 仿真类型: {response['simulation_type']}")
        
        # 显示参数
        print("\n📋 使用的参数:")
        for key, value in response['parameters'].items():
            if key != 'f':
                print(f"  {key}: {value}")
        
        # 显示数值结果
        result = response['result']
        params = response['parameters']
        N_length = params['N_length']
        N_width = params['N_width']
        b = params['b']
        
        x_solution_up = result[:N_length * N_width]
        x_solution_down = result[N_length * N_width:2 * N_length * N_width]
        voltage = result[-1]
        
        total_charge_up = x_solution_up.sum().item() * b**2
        total_charge_down = x_solution_down.sum().item() * b**2
        
        print(f"\n📈 数值结果:")
        print(f"  上极板总电荷: {total_charge_up:.6f}")
        print(f"  下极板总电荷: {total_charge_down:.6f}")
        print(f"  电压: {voltage.item():.6f}")
        print(f"  解向量维度: {result.shape[0]}")
        
        # 可视化结果
        print("\n📊 生成可视化图表...")
        
        # 重塑为矩阵
        up_matrix = x_solution_up.detach().cpu().numpy().reshape(N_length, N_width)
        down_matrix = x_solution_down.detach().cpu().numpy().reshape(N_length, N_width)
        
        # 创建图表
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))
        
        # 上极板
        im1 = ax1.imshow(up_matrix, cmap='RdBu', aspect='auto')
        ax1.set_title('上极板电荷分布')
        ax1.set_xlabel('宽度方向')
        ax1.set_ylabel('长度方向')
        plt.colorbar(im1, ax=ax1)
        
        # 下极板
        im2 = ax2.imshow(down_matrix, cmap='RdBu', aspect='auto')
        ax2.set_title('下极板电荷分布')
        ax2.set_xlabel('宽度方向')
        ax2.set_ylabel('长度方向')
        plt.colorbar(im2, ax=ax2)
        
        plt.suptitle('电荷分布仿真结果')
        plt.tight_layout()
        plt.show()
        
        return True
    else:
        print(f"❌ 仿真失败: {response['error']}")
        return False

def demo_transfer_charge():
    """演示转移电荷仿真"""
    print_section("转移电荷仿真演示")
    
    # 创建智能体
    api_key = "sk-darna2nrdmzrwbcx"
    agent = ElectromagneticAgent(api_key)
    
    # 测试用例
    test_input = "分析转移电荷变化，网格大小20x20，时间从0到5，步数50，运动方程0.6+0.4*sin(t)"
    
    print(f"输入: {test_input}")
    print("🤖 智能体正在分析...")
    
    start_time = time.time()
    response = agent.process_request(test_input)
    end_time = time.time()
    
    if response['success']:
        print(f"✅ 仿真成功完成！(耗时: {end_time-start_time:.2f}秒)")
        print(f"📊 仿真类型: {response['simulation_type']}")
        
        # 显示参数
        print("\n📋 使用的参数:")
        for key, value in response['parameters'].items():
            if key != 'f':
                print(f"  {key}: {value}")
        
        # 显示图表
        fig, ax = response['result']
        
        # 从图表中提取数据
        line = ax.get_lines()[0]
        x_data = line.get_xdata()
        y_data = line.get_ydata()
        
        print(f"\n📈 统计信息:")
        print(f"  最大转移电荷: {np.max(y_data):.6f}")
        print(f"  最小转移电荷: {np.min(y_data):.6f}")
        print(f"  平均转移电荷: {np.mean(y_data):.6f}")
        print(f"  标准差: {np.std(y_data):.6f}")
        
        print("\n📊 显示图表...")
        plt.show()
        
        return True
    else:
        print(f"❌ 仿真失败: {response['error']}")
        return False

def demo_electric_current():
    """演示电流输出仿真"""
    print_section("电流输出仿真演示")
    
    # 创建智能体
    api_key = "sk-darna2nrdmzrwbcx"
    agent = ElectromagneticAgent(api_key)
    
    # 测试用例
    test_input = "计算电流输出，N_length=20，N_width=20，时间范围0-3，步数30，运动方程1.0+0.3*cos(t)"
    
    print(f"输入: {test_input}")
    print("🤖 智能体正在分析...")
    
    start_time = time.time()
    response = agent.process_request(test_input)
    end_time = time.time()
    
    if response['success']:
        print(f"✅ 仿真成功完成！(耗时: {end_time-start_time:.2f}秒)")
        print(f"📊 仿真类型: {response['simulation_type']}")
        
        # 显示参数
        print("\n📋 使用的参数:")
        for key, value in response['parameters'].items():
            if key != 'f':
                print(f"  {key}: {value}")
        
        # 显示图表
        fig, ax = response['result']
        
        # 从图表中提取数据
        line = ax.get_lines()[0]
        x_data = line.get_xdata()
        y_data = line.get_ydata()
        
        print(f"\n📈 统计信息:")
        print(f"  最大电流: {np.max(y_data):.6f}")
        print(f"  最小电流: {np.min(y_data):.6f}")
        print(f"  平均电流: {np.mean(y_data):.6f}")
        print(f"  标准差: {np.std(y_data):.6f}")
        
        print("\n📊 显示图表...")
        plt.show()
        
        return True
    else:
        print(f"❌ 仿真失败: {response['error']}")
        return False

def demo_parameter_parsing():
    """演示参数解析功能"""
    print_section("参数解析功能演示")
    
    from parameter_parser import ParameterParser
    parser = ParameterParser()
    
    test_cases = [
        "N_length=50, N_width=60, b=1.5, rt=-1.2, d=0.8",
        "长度网格100，宽度网格80，基础尺度2.0，sigma参数1.5",
        "时间从0到10，步数200，运动方程0.5+0.3*sin(t)",
        "计算电荷分布，网格大小40x40，深度0.5，高度0.7",
        "转移电荷仿真，时间范围0-8，epsilon参数3.0"
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n测试 {i}: {test_case}")
        
        # 解析参数
        params = parser.parse_parameters(test_case)
        
        # 检测仿真类型
        sim_type = parser.detect_simulation_type(test_case)
        
        print(f"  检测到的仿真类型: {sim_type}")
        print("  解析出的参数:")
        for key, value in params.items():
            if key != 'f':
                print(f"    {key}: {value}")

def main():
    """主演示函数"""
    print_header("电磁场仿真智能体功能演示")
    
    print("🎯 本演示将展示智能体的三种核心功能：")
    print("1. 电荷分布模拟")
    print("2. 转移电荷模拟") 
    print("3. 输出电流模拟")
    print("4. 参数解析功能")
    
    input("\n按回车键开始演示...")
    
    # 演示参数解析
    demo_parameter_parsing()
    
    input("\n按回车键继续演示电荷分布仿真...")
    
    # 演示电荷分布仿真
    success1 = demo_charge_distribution()
    
    if success1:
        input("\n按回车键继续演示转移电荷仿真...")
        
        # 演示转移电荷仿真
        success2 = demo_transfer_charge()
        
        if success2:
            input("\n按回车键继续演示电流输出仿真...")
            
            # 演示电流输出仿真
            success3 = demo_electric_current()
            
            # 总结
            print_header("演示总结")
            
            total_success = sum([success1, success2, success3])
            print(f"📊 演示结果: {total_success}/3 个功能演示成功")
            
            if total_success == 3:
                print("🎉 所有功能演示成功！智能体工作正常")
                print("\n🚀 您现在可以：")
                print("1. 运行 python simple_gui.py 使用图形界面")
                print("2. 运行 python app.py 使用Web界面")
                print("3. 运行 python main.py 使用命令行界面")
            else:
                print("⚠️ 部分功能演示失败，请检查配置")
        else:
            print("❌ 转移电荷仿真演示失败，跳过后续演示")
    else:
        print("❌ 电荷分布仿真演示失败，跳过后续演示")
    
    print("\n👋 演示结束，感谢使用电磁场仿真智能体！")

if __name__ == "__main__":
    main()
