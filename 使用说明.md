# 电磁场仿真智能体使用说明

## 🚀 快速启动

### 方式一：简化GUI界面（推荐新手）
```bash
python simple_gui.py
```
- ✅ 无需额外依赖安装
- ✅ 图形化界面，操作简单
- ✅ 实时结果显示

### 方式二：Web界面（推荐高级用户）
```bash
python run_app.py
```
- ✅ 现代化Web界面
- ✅ 响应式设计
- ✅ 交互式图表

### 方式三：命令行界面
```bash
python main.py
```
- ✅ 轻量级运行
- ✅ 适合脚本调用

## 📱 界面使用指南

### 简化GUI界面操作

#### 1. 模式选择
- **💬 智能对话模式**：通过自然语言描述仿真需求
- **⚙️ 手动参数模式**：直接设置具体参数值

#### 2. 仿真类型选择
- **电荷分布模拟**：计算静态电荷分布
- **转移电荷模拟**：分析电荷随时间的转移
- **输出电流模拟**：计算电流随时间的变化

#### 3. 智能对话模式使用
1. 在文本框中输入需求描述
2. 点击"🚀 发送"按钮
3. 查看智能体回复和仿真结果
4. 结果显示在右侧面板的不同标签页中

**示例输入**：
```
计算电荷分布，网格大小50x50，b=1，rt=1，d=0.6，z=0.6，epsilon=2.2
```

```
分析转移电荷变化，网格30x30，时间从0到5，步数100，运动方程0.6+0.4*sin(t)
```

#### 4. 手动参数模式使用
1. 切换到"⚙️ 手动参数"模式
2. 在左侧参数面板设置各项参数
3. 选择仿真类型
4. 点击"🚀 执行仿真"
5. 查看右侧结果显示

#### 5. 结果查看
- **📊 图表标签页**：显示可视化结果
  - 电荷分布：热力图显示上下极板电荷分布
  - 时间序列：线图显示随时间变化的趋势
- **📋 数据标签页**：显示数值结果和参数信息
- **📝 日志标签页**：显示运行日志和状态信息

## 🎯 使用示例

### 示例1：电荷分布仿真
**目标**：计算60x60网格的电荷分布

**智能对话输入**：
```
计算电荷分布，长度网格60，宽度网格60，基础尺度1，sigma参数1，深度0.6，高度0.6，epsilon参数2.2
```

**手动参数设置**：
- 长度网格：60
- 宽度网格：60
- 尺度参数：1.0
- Sigma参数：1.0
- 深度参数：0.6
- 高度参数：0.6
- Epsilon参数：2.2

**预期结果**：
- 上下极板电荷分布热力图
- 总电荷量、电压等数值结果

### 示例2：转移电荷仿真
**目标**：分析正弦运动下的电荷转移

**智能对话输入**：
```
转移电荷仿真，网格大小40x40，时间从0到10，步数200，运动方程0.6+0.4*sin(t)
```

**手动参数设置**：
- 基本参数：N_length=40, N_width=40
- 时间参数：t_start=0, t_end=10, T=200
- 函数类型：sin
- 或自定义函数：0.6+0.4*sin(t)

**预期结果**：
- 转移电荷随时间变化的曲线图
- 最大值、最小值、平均值等统计信息

### 示例3：电流输出仿真
**目标**：计算电流随时间的变化

**智能对话输入**：
```
电流输出仿真，网格30x30，时间范围0-5，步数100，运动方程1.0+0.3*cos(t)
```

**预期结果**：
- 电流随时间变化的曲线图
- 电流统计信息

## ⚙️ 参数说明

### 基本参数
| 参数 | 说明 | 典型值 | 范围 |
|------|------|--------|------|
| N_length | 长度方向网格数 | 60 | 10-200 |
| N_width | 宽度方向网格数 | 60 | 10-200 |
| b | 基础尺度参数 | 1.0 | 0.1-10 |
| rt | Sigma参数 | 1.0 | -10-10 |
| d | 深度参数 | 0.6 | 0.1-5 |
| z | 高度参数 | 0.6 | 0.1-5 |
| ebsenr | Epsilon参数 | 2.2 | 0.1-10 |

### 时间参数（仅时间相关仿真）
| 参数 | 说明 | 典型值 | 范围 |
|------|------|--------|------|
| t_start | 开始时间 | 0.0 | 0-100 |
| t_end | 结束时间 | 10.0 | 0.1-100 |
| T | 时间步数 | 100 | 10-1000 |

### 运动方程
- **正弦函数**：`a + b*sin(t)`
- **余弦函数**：`a + b*cos(t)`
- **常数函数**：`c`
- **自定义**：支持基本数学表达式

## 🔧 故障排除

### 常见问题

#### 1. 界面无法启动
**问题**：运行`python simple_gui.py`后无反应
**解决**：
- 检查Python环境是否正确
- 确保tkinter已安装（通常随Python自带）
- 尝试运行`python -c "import tkinter; print('OK')"`

#### 2. API连接失败
**问题**：智能对话模式无法工作
**解决**：
- 检查网络连接
- 确认API密钥有效
- 尝试手动参数模式

#### 3. 计算时间过长
**问题**：仿真执行时间很长
**解决**：
- 减小网格大小（如从100x100改为50x50）
- 减少时间步数
- 使用GPU加速（如果可用）

#### 4. 内存不足
**问题**：大网格计算时内存溢出
**解决**：
- 减小网格大小
- 关闭其他程序释放内存
- 分批计算

#### 5. 图表显示异常
**问题**：结果图表无法正常显示
**解决**：
- 检查matplotlib是否正确安装
- 重启应用程序
- 检查数据是否有效

### 性能优化建议

1. **网格大小**：
   - 测试时使用小网格（20x20）
   - 正式计算使用中等网格（50x50）
   - 高精度需求使用大网格（100x100+）

2. **时间步数**：
   - 快速预览：50-100步
   - 标准分析：100-200步
   - 精细分析：200-500步

3. **硬件加速**：
   - 如有NVIDIA GPU，确保CUDA可用
   - 使用device='cuda'参数

## 📞 技术支持

如遇到问题，请：
1. 查看日志标签页的错误信息
2. 检查参数设置是否合理
3. 尝试简化参数重新运行
4. 参考本文档的故障排除部分

## 🎓 进阶使用

### 自定义运动方程
支持的数学函数：
- `sin(t)`, `cos(t)` - 三角函数
- `exp(t)` - 指数函数
- `log(t)` - 对数函数
- 基本运算：`+`, `-`, `*`, `/`, `**`

示例：
```
0.5 + 0.3*sin(t) + 0.1*cos(2*t)
1.0*exp(-0.1*t)*sin(t)
```

### 批量计算
可以通过修改参数进行多次计算，比较不同参数下的结果。

### 结果导出
- 图表可以通过右键保存
- 数据可以复制到剪贴板
- 支持多种图片格式导出
