{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import torch\n", "import numpy as np\n", "# 检查是否有可用的 GPU\n", "device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["N = 100\n", "b = 0.1\n", "rt = 5*10**-4\n", "d = 0.5\n", "q = rt*N**2            #q = rt * N**2\n", "ebsenr = 2.2\n", "ebr = torch.tensor(-(ebsenr - 1) / (ebsenr * 2), device=device, dtype=torch.float32)  # 直接在 GPU 上定义 ebr"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[ 0.5  0.5  0.5 ... 99.5 99.5 99.5]\n", "[ 0.5  1.5  2.5 ... 97.5 98.5 99.5]\n"]}], "source": ["for zt in range(0, 100):\n", "    esum=[]\n", "    z = zt / 10\n", "    i, j = np.meshgrid(np.arange(1, N+1), np.arange(1, N+1), indexing='ij')\n", "    x = (i - 1/2).flatten() * b\n", "    y = (j - 1/2).flatten() * b\n", "    # 将坐标转换为张量并移动到 GPU\n", "    x = torch.tensor(x, device=device, dtype=torch.float32)\n", "    y = torch.tensor(y, device=device, dtype=torch.float32)\n", "\n", "    # 将 d 和 z 转换为张量\n", "    d_tensor = torch.tensor(d, device=device, dtype=torch.float32)\n", "    z_tensor = torch.tensor(z, device=device, dtype=torch.float32)\n", "\n", "    # 计算距离矩阵\n", "    dist = torch.sqrt((x[:, None] - x[None, :])**2 + (y[:, None] - y[None, :])**2)\n", "    dist_d = torch.sqrt(dist**2 + d_tensor**2)\n", "    dist_z = torch.sqrt(dist**2 + z_tensor**2)\n", "    dist_dz = torch.sqrt(dist**2 + (d_tensor + z_tensor)**2)\n", "\n", "    # 创建矩阵\n", "    lt = torch.where(dist == 0, 4 * b * 0.8814, b**2 / dist)\n", "    ltd = torch.where(dist_d ==0, 2 * np.pi * (b / 2) * (-d_tensor / (b / 2) + torch.sqrt((d_tensor / (b / 2))**2 + 4 / np.pi)), b**2 / dist_d)\n", "    ltz = torch.where(dist_z ==0, 2 * np.pi * (b / 2) * (-z_tensor / (b / 2) + torch.sqrt((z_tensor / (b / 2))**2 + 4 / np.pi)), b**2 / dist_z)\n", "    ltdz = torch.where(dist_dz ==0, 2 * np.pi * (b / 2) * (-(d_tensor+z_tensor) / (b / 2) + torch.sqrt(((d_tensor+z_tensor) / (b / 2))**2 + 4 / np.pi)), b**2 / dist_dz)\n", "\n", "    A = ltdz - ebr * (lt - ltd)\n", "    B = lt + ebr * (lt - ltd)\n", "    D = lt + ebr * (ltz - ltdz)\n", "    E = ltdz - ebr * (ltz - ltdz)\n", "    # 将矩阵合成为一个矩阵\n", "    H_top = torch.cat((A, B), dim=1)\n", "    H_bottom = torch.cat((D, E), dim=1)\n", "    H = torch.cat((H_top, H_bottom), dim=0)\n", "    # 创建一个全为 -1 的列向量\n", "    col_neg_ones = -torch.ones((H.shape[0], 1), device=device, dtype=torch.float32)\n", "    # 创建一个全为 1 的行向量\n", "    row_ones = torch.ones((1, H.shape[1] + 1), device=device, dtype=torch.float32)\n", "    # 将列向量添加到矩阵 H 的最右侧\n", "    H = torch.hstack((H, col_neg_ones))\n", "    # 将行向量添加到矩阵 H 的最底部\n", "    H = torch.vstack((H, row_ones))\n", "    # 将右下角的交界处设置为 0\n", "    H[-1, -1] = 0\n", "    C = ltd - ebr * (lt - ltd)\n", "    F = ltz + ebr * (ltz - ltdz)\n", "    sigmat = torch.full((N**2,), rt, device=device, dtype=torch.float32)  # 确保 sigmat 已经正确初始化\n", "    # 计算矩阵 C 和 F 乘以向量 sigmat\n", "    C_sigmat = torch.matmul(C, sigmat)\n", "    F_sigmat = torch.matmul(F, sigmat)\n", "    # 合并结果向量，并在最后添加一个元素 q\n", "    result_vector = torch.cat((C_sigmat, F_sigmat, torch.tensor([q], device=device, dtype=torch.float32)))\n", "    # 求解 Hx = result_vector\n", "    x_solution = torch.linalg.solve(H, result_vector)\n", "    # 计算 xt 的前 N^2 个元素的和\n", "    x_solution_sum = torch.sum(x_solution[:N**2])\n", "    results_solution = b**2 * x_solution_sum.detach().cpu().numpy()\n", "    print(z, results_solution)\n", "    esum.append(results_solution)\n", "\n", "print(esum)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "xypytorch", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.19"}}, "nbformat": 4, "nbformat_minor": 4}