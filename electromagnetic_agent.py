import json
import matplotlib.pyplot as plt
from typing import Dict, Any, Optional
from api_client import DeepSeekClient
from parameter_parser import ParameterParser
from src.e_long import compute_solution_long
from src.i_long import plot_transfer_charges, plot_electric_currents

class ElectromagneticAgent:
    """电磁场仿真大模型智能体"""
    
    def __init__(self, api_key: str):
        self.client = DeepSeekClient(api_key)
        self.parser = ParameterParser()
        
        # 系统提示词
        self.system_prompt = """你是一个电磁场仿真专家助手。你可以帮助用户进行以下三种电磁场仿真：

1. 电荷分布模拟 - 计算矩形电动力学器件上下极板的电荷分布
2. 转移电荷模拟 - 分析随时间变化的电荷转移情况
3. 输出电流模拟 - 计算随时间变化的电流输出

请根据用户的需求，识别他们想要进行哪种仿真，并提取相关参数。

参数说明：
- N_length: 长度方向网格数量 (整数)
- N_width: 宽度方向网格数量 (整数)  
- b: 基础尺度参数 (浮点数)
- rt: sigma参数 (浮点数，可为负值)
- d: 深度参数 (浮点数)
- z: 高度参数 (浮点数，仅电荷分布模拟需要)
- ebsenr: epsilon参数 (浮点数)
- t_start: 开始时间 (浮点数，仅时间相关仿真需要)
- t_end: 结束时间 (浮点数，仅时间相关仿真需要)
- T: 时间步数 (整数，仅时间相关仿真需要)
- f: 运动方程函数 (如: 0.6+0.4*sin(t))

请以JSON格式回复，包含：
{
    "simulation_type": "charge_distribution" | "transfer_charge" | "electric_current",
    "parameters": {参数字典},
    "explanation": "对用户需求的理解说明"
}"""
    
    def analyze_user_request(self, user_input: str) -> Dict[str, Any]:
        """分析用户请求并提取参数"""
        messages = [
            {"role": "system", "content": self.system_prompt},
            {"role": "user", "content": user_input}
        ]
        
        try:
            response = self.client.chat_completion(messages)
            content = self.client.extract_content(response)
            
            # 尝试解析JSON响应
            try:
                result = json.loads(content)
                return result
            except json.JSONDecodeError:
                # 如果JSON解析失败，使用参数解析器
                params = self.parser.parse_parameters(user_input)
                sim_type = self.parser.detect_simulation_type(user_input)

                return {
                    "simulation_type": sim_type,
                    "parameters": params,
                    "explanation": "基于关键词分析的仿真需求"
                }
                
        except Exception as e:
            # 备用解析方案
            params = self.parser.parse_parameters(user_input)
            sim_type = self.parser.detect_simulation_type(user_input)
            return {
                "simulation_type": sim_type,
                "parameters": params,
                "explanation": f"使用备用解析方案，错误: {str(e)}"
            }
    
    def run_simulation(self, simulation_type: str, parameters: Dict[str, Any]) -> Any:
        """执行仿真计算"""
        try:
            # 验证参数
            validated_params = self.parser.validate_parameters(parameters, simulation_type)
            
            if simulation_type == "charge_distribution":
                return self._run_charge_distribution(validated_params)
            elif simulation_type == "transfer_charge":
                return self._run_transfer_charge(validated_params)
            elif simulation_type == "electric_current":
                return self._run_electric_current(validated_params)
            else:
                raise ValueError(f"未知的仿真类型: {simulation_type}")
                
        except Exception as e:
            raise Exception(f"仿真执行失败: {str(e)}")
    
    def _run_charge_distribution(self, params: Dict[str, Any]):
        """执行电荷分布仿真"""
        result = compute_solution_long(
            N_length=params['N_length'],
            N_width=params['N_width'],
            b=params['b'],
            rt=params['rt'],
            d=params['d'],
            z=params['z'],
            ebsenr=params['ebsenr'],
            device=params.get('device')
        )
        return result
    
    def _run_transfer_charge(self, params: Dict[str, Any]):
        """执行转移电荷仿真"""
        fig, ax = plot_transfer_charges(
            N_length=params['N_length'],
            N_width=params['N_width'],
            b=params['b'],
            rt=params['rt'],
            d=params['d'],
            t_start=params['t_start'],
            t_end=params['t_end'],
            T=params['T'],
            f=params['f'],
            ebsenr=params['ebsenr'],
            save_path=params.get('save_path'),
            device=params.get('device')
        )
        return fig, ax
    
    def _run_electric_current(self, params: Dict[str, Any]):
        """执行电流输出仿真"""
        fig, ax = plot_electric_currents(
            N_length=params['N_length'],
            N_width=params['N_width'],
            b=params['b'],
            rt=params['rt'],
            d=params['d'],
            t_start=params['t_start'],
            t_end=params['t_end'],
            T=params['T'],
            f=params['f'],
            ebsenr=params['ebsenr'],
            save_path=params.get('save_path'),
            device=params.get('device')
        )
        return fig, ax
    
    def process_request(self, user_input: str) -> Dict[str, Any]:
        """处理用户请求的主要方法"""
        try:
            # 分析用户请求
            analysis = self.analyze_user_request(user_input)
            
            # 执行仿真
            result = self.run_simulation(
                analysis['simulation_type'], 
                analysis['parameters']
            )
            
            return {
                "success": True,
                "simulation_type": analysis['simulation_type'],
                "parameters": analysis['parameters'],
                "result": result,
                "explanation": analysis.get('explanation', '')
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "simulation_type": None,
                "parameters": None,
                "result": None
            }
