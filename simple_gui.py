#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
电磁场仿真智能体简化GUI界面
使用tkinter实现，无需额外依赖
"""

import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox, filedialog
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import numpy as np
import threading
from electromagnetic_agent import ElectromagneticAgent
from parameter_parser import ParameterParser

class ElectromagneticGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("🔬 电磁场仿真智能体")
        self.root.geometry("1200x800")
        
        # 初始化智能体
        self.api_key = "sk-darna2nrdmzrwbcx"
        self.agent = ElectromagneticAgent(self.api_key)
        self.parser = ParameterParser()
        
        # 创建界面
        self.create_widgets()
        
        # 聊天历史
        self.chat_history = []
        
    def create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 左侧面板 - 控制区域
        left_frame = ttk.LabelFrame(main_frame, text="🎛️ 控制面板", padding=10)
        left_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 10))
        
        # 右侧面板 - 结果显示区域
        right_frame = ttk.LabelFrame(main_frame, text="📊 结果显示", padding=10)
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)
        
        self.create_control_panel(left_frame)
        self.create_result_panel(right_frame)
        
    def create_control_panel(self, parent):
        """创建控制面板"""
        # 模式选择
        mode_frame = ttk.LabelFrame(parent, text="模式选择", padding=5)
        mode_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.mode_var = tk.StringVar(value="chat")
        ttk.Radiobutton(mode_frame, text="💬 智能对话", variable=self.mode_var, 
                       value="chat", command=self.on_mode_change).pack(anchor=tk.W)
        ttk.Radiobutton(mode_frame, text="⚙️ 手动参数", variable=self.mode_var, 
                       value="manual", command=self.on_mode_change).pack(anchor=tk.W)
        
        # 仿真类型选择
        type_frame = ttk.LabelFrame(parent, text="仿真类型", padding=5)
        type_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.sim_type_var = tk.StringVar(value="charge_distribution")
        ttk.Radiobutton(type_frame, text="电荷分布模拟", variable=self.sim_type_var, 
                       value="charge_distribution").pack(anchor=tk.W)
        ttk.Radiobutton(type_frame, text="转移电荷模拟", variable=self.sim_type_var, 
                       value="transfer_charge").pack(anchor=tk.W)
        ttk.Radiobutton(type_frame, text="输出电流模拟", variable=self.sim_type_var, 
                       value="electric_current").pack(anchor=tk.W)
        
        # 对话区域
        self.chat_frame = ttk.LabelFrame(parent, text="💬 智能对话", padding=5)
        self.chat_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # 聊天历史
        self.chat_display = scrolledtext.ScrolledText(self.chat_frame, height=15, width=40)
        self.chat_display.pack(fill=tk.BOTH, expand=True, pady=(0, 5))
        
        # 输入框
        self.chat_input = tk.Text(self.chat_frame, height=3, width=40)
        self.chat_input.pack(fill=tk.X, pady=(0, 5))
        
        # 对话按钮
        chat_btn_frame = ttk.Frame(self.chat_frame)
        chat_btn_frame.pack(fill=tk.X)
        
        ttk.Button(chat_btn_frame, text="🚀 发送", 
                  command=self.send_chat_message).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(chat_btn_frame, text="🗑️ 清空", 
                  command=self.clear_chat).pack(side=tk.LEFT)
        
        # 手动参数区域
        self.manual_frame = ttk.LabelFrame(parent, text="⚙️ 参数设置", padding=5)
        
        self.create_parameter_inputs()
        
        # 执行按钮
        ttk.Button(parent, text="🚀 执行仿真", 
                  command=self.run_simulation).pack(fill=tk.X, pady=10)
        
        # 初始显示对话模式
        self.on_mode_change()
        
    def create_parameter_inputs(self):
        """创建参数输入控件"""
        # 基本参数
        basic_frame = ttk.LabelFrame(self.manual_frame, text="基本参数", padding=5)
        basic_frame.pack(fill=tk.X, pady=(0, 5))
        
        # 参数变量
        self.params = {}
        
        # 网格参数
        grid_frame = ttk.Frame(basic_frame)
        grid_frame.pack(fill=tk.X, pady=2)
        ttk.Label(grid_frame, text="长度网格:").pack(side=tk.LEFT)
        self.params['N_length'] = tk.IntVar(value=60)
        ttk.Entry(grid_frame, textvariable=self.params['N_length'], width=10).pack(side=tk.RIGHT)
        
        grid_frame2 = ttk.Frame(basic_frame)
        grid_frame2.pack(fill=tk.X, pady=2)
        ttk.Label(grid_frame2, text="宽度网格:").pack(side=tk.LEFT)
        self.params['N_width'] = tk.IntVar(value=60)
        ttk.Entry(grid_frame2, textvariable=self.params['N_width'], width=10).pack(side=tk.RIGHT)
        
        # 物理参数
        for param, label, default in [
            ('b', '尺度参数:', 1.0),
            ('rt', 'Sigma参数:', 1.0),
            ('d', '深度参数:', 0.6),
            ('z', '高度参数:', 0.6),
            ('ebsenr', 'Epsilon参数:', 2.2)
        ]:
            frame = ttk.Frame(basic_frame)
            frame.pack(fill=tk.X, pady=2)
            ttk.Label(frame, text=label).pack(side=tk.LEFT)
            self.params[param] = tk.DoubleVar(value=default)
            ttk.Entry(frame, textvariable=self.params[param], width=10).pack(side=tk.RIGHT)
        
        # 时间参数
        time_frame = ttk.LabelFrame(self.manual_frame, text="时间参数", padding=5)
        time_frame.pack(fill=tk.X, pady=(0, 5))
        
        for param, label, default in [
            ('t_start', '开始时间:', 0.0),
            ('t_end', '结束时间:', 10.0),
            ('T', '时间步数:', 100)
        ]:
            frame = ttk.Frame(time_frame)
            frame.pack(fill=tk.X, pady=2)
            ttk.Label(frame, text=label).pack(side=tk.LEFT)
            if param == 'T':
                self.params[param] = tk.IntVar(value=default)
            else:
                self.params[param] = tk.DoubleVar(value=default)
            ttk.Entry(frame, textvariable=self.params[param], width=10).pack(side=tk.RIGHT)
        
        # 运动方程
        func_frame = ttk.LabelFrame(self.manual_frame, text="运动方程", padding=5)
        func_frame.pack(fill=tk.X)
        
        ttk.Label(func_frame, text="函数类型:").pack(anchor=tk.W)
        self.func_type_var = tk.StringVar(value="sin")
        func_combo = ttk.Combobox(func_frame, textvariable=self.func_type_var, 
                                 values=["sin", "cos", "constant", "custom"])
        func_combo.pack(fill=tk.X, pady=2)
        
        ttk.Label(func_frame, text="自定义函数:").pack(anchor=tk.W)
        self.func_entry = tk.Entry(func_frame)
        self.func_entry.pack(fill=tk.X, pady=2)
        self.func_entry.insert(0, "0.6+0.4*sin(t)")
        
    def create_result_panel(self, parent):
        """创建结果显示面板"""
        # 创建notebook用于多标签页
        self.notebook = ttk.Notebook(parent)
        self.notebook.pack(fill=tk.BOTH, expand=True)
        
        # 图表标签页
        self.plot_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.plot_frame, text="📊 图表")
        
        # 数据标签页
        self.data_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.data_frame, text="📋 数据")
        
        # 日志标签页
        self.log_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.log_frame, text="📝 日志")
        
        # 创建图表区域
        self.figure = plt.Figure(figsize=(8, 6), dpi=100)
        self.canvas = FigureCanvasTkAgg(self.figure, self.plot_frame)
        self.canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
        
        # 创建数据显示区域
        self.data_display = scrolledtext.ScrolledText(self.data_frame)
        self.data_display.pack(fill=tk.BOTH, expand=True)
        
        # 创建日志显示区域
        self.log_display = scrolledtext.ScrolledText(self.log_frame)
        self.log_display.pack(fill=tk.BOTH, expand=True)
        
    def on_mode_change(self):
        """模式切换处理"""
        if self.mode_var.get() == "chat":
            self.chat_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
            self.manual_frame.pack_forget()
        else:
            self.chat_frame.pack_forget()
            self.manual_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
    
    def send_chat_message(self):
        """发送聊天消息"""
        message = self.chat_input.get("1.0", tk.END).strip()
        if not message:
            return
        
        # 显示用户消息
        self.add_chat_message("用户", message)
        self.chat_input.delete("1.0", tk.END)
        
        # 在新线程中处理请求
        threading.Thread(target=self.process_chat_request, args=(message,), daemon=True).start()
    
    def process_chat_request(self, message):
        """处理聊天请求"""
        try:
            self.add_log("🤖 正在分析用户需求...")
            response = self.agent.process_request(message)
            
            if response['success']:
                agent_message = f"✅ 仿真完成！\n类型: {response['simulation_type']}\n说明: {response.get('explanation', '')}"
                self.add_chat_message("智能体", agent_message)
                
                # 显示结果
                self.root.after(0, lambda: self.display_results(response))
            else:
                error_msg = f"❌ 仿真失败: {response['error']}"
                self.add_chat_message("智能体", error_msg)
                
        except Exception as e:
            error_msg = f"❌ 发生错误: {str(e)}"
            self.add_chat_message("智能体", error_msg)
    
    def run_simulation(self):
        """运行仿真"""
        try:
            # 获取参数
            params = self.get_manual_parameters()
            sim_type = self.sim_type_var.get()
            
            self.add_log(f"🚀 开始执行{sim_type}仿真...")
            
            # 在新线程中执行仿真
            threading.Thread(target=self.execute_simulation, args=(sim_type, params), daemon=True).start()
            
        except Exception as e:
            messagebox.showerror("错误", f"参数错误: {str(e)}")
    
    def execute_simulation(self, sim_type, params):
        """执行仿真计算"""
        try:
            result = self.agent.run_simulation(sim_type, params)
            
            response = {
                'success': True,
                'simulation_type': sim_type,
                'parameters': params,
                'result': result,
                'explanation': f"手动参数模式 - {sim_type}"
            }
            
            # 在主线程中显示结果
            self.root.after(0, lambda: self.display_results(response))
            self.add_log("✅ 仿真执行成功！")
            
        except Exception as e:
            self.add_log(f"❌ 仿真失败: {str(e)}")
            messagebox.showerror("仿真错误", str(e))
    
    def get_manual_parameters(self):
        """获取手动输入的参数"""
        params = {}
        
        # 获取数值参数
        for key, var in self.params.items():
            params[key] = var.get()
        
        # 获取函数参数
        func_type = self.func_type_var.get()
        if func_type == "sin":
            params['f'] = lambda t: 0.6 + 0.4 * np.sin(t)
        elif func_type == "cos":
            params['f'] = lambda t: 0.6 + 0.4 * np.cos(t)
        elif func_type == "constant":
            params['f'] = lambda t: 1.0
        else:
            func_str = self.func_entry.get()
            params['f'] = self.parser.parse_function(func_str)
        
        params['save_path'] = None
        params['device'] = None
        
        return params
    
    def display_results(self, response):
        """显示仿真结果"""
        simulation_type = response['simulation_type']
        result = response['result']
        params = response['parameters']
        
        # 清空之前的图表
        self.figure.clear()
        
        if simulation_type == "charge_distribution":
            self.plot_charge_distribution(result, params)
        else:
            self.plot_time_series(result, simulation_type)
        
        # 更新画布
        self.canvas.draw()
        
        # 显示数据
        self.show_data(response)
        
        # 切换到图表标签页
        self.notebook.select(0)
    
    def plot_charge_distribution(self, result, params):
        """绘制电荷分布图"""
        N_length = params['N_length']
        N_width = params['N_width']
        
        # 提取数据
        x_solution_up = result[:N_length * N_width].detach().cpu().numpy()
        x_solution_down = result[N_length * N_width:2 * N_length * N_width].detach().cpu().numpy()
        
        # 重塑为矩阵
        up_matrix = x_solution_up.reshape(N_length, N_width)
        down_matrix = x_solution_down.reshape(N_length, N_width)
        
        # 创建子图
        ax1 = self.figure.add_subplot(121)
        ax2 = self.figure.add_subplot(122)
        
        # 绘制热力图
        im1 = ax1.imshow(up_matrix, cmap='RdBu', aspect='auto')
        ax1.set_title('上极板电荷分布')
        ax1.set_xlabel('宽度方向')
        ax1.set_ylabel('长度方向')
        self.figure.colorbar(im1, ax=ax1)
        
        im2 = ax2.imshow(down_matrix, cmap='RdBu', aspect='auto')
        ax2.set_title('下极板电荷分布')
        ax2.set_xlabel('宽度方向')
        ax2.set_ylabel('长度方向')
        self.figure.colorbar(im2, ax=ax2)
        
        self.figure.suptitle('电荷分布仿真结果')
        self.figure.tight_layout()
    
    def plot_time_series(self, result, simulation_type):
        """绘制时间序列图"""
        fig, ax = result
        
        # 从matplotlib图形中提取数据
        line = ax.get_lines()[0]
        x_data = line.get_xdata()
        y_data = line.get_ydata()
        
        # 在新图中绘制
        ax_new = self.figure.add_subplot(111)
        ax_new.plot(x_data, y_data, 'b-', linewidth=2)
        
        title = "转移电荷随时间变化" if simulation_type == "transfer_charge" else "电流随时间变化"
        y_label = "转移电荷" if simulation_type == "transfer_charge" else "电流"
        
        ax_new.set_title(title)
        ax_new.set_xlabel('时间')
        ax_new.set_ylabel(y_label)
        ax_new.grid(True, alpha=0.3)
        
        self.figure.tight_layout()
    
    def show_data(self, response):
        """显示数据信息"""
        self.data_display.delete("1.0", tk.END)
        
        data_text = f"仿真类型: {response['simulation_type']}\n"
        data_text += f"说明: {response.get('explanation', '')}\n\n"
        data_text += "参数设置:\n"
        
        for key, value in response['parameters'].items():
            if key != 'f':
                data_text += f"  {key}: {value}\n"
        
        if response['simulation_type'] == "charge_distribution":
            result = response['result']
            params = response['parameters']
            N_length = params['N_length']
            N_width = params['N_width']
            b = params['b']
            
            x_solution_up = result[:N_length * N_width]
            x_solution_down = result[N_length * N_width:2 * N_length * N_width]
            voltage = result[-1]
            
            total_charge_up = x_solution_up.sum().item() * b**2
            total_charge_down = x_solution_down.sum().item() * b**2
            
            data_text += f"\n数值结果:\n"
            data_text += f"  上极板总电荷: {total_charge_up:.6f}\n"
            data_text += f"  下极板总电荷: {total_charge_down:.6f}\n"
            data_text += f"  电压: {voltage.item():.6f}\n"
            data_text += f"  解向量维度: {result.shape[0]}\n"
        
        self.data_display.insert("1.0", data_text)
    
    def add_chat_message(self, sender, message):
        """添加聊天消息"""
        self.chat_display.insert(tk.END, f"{sender}: {message}\n\n")
        self.chat_display.see(tk.END)
    
    def add_log(self, message):
        """添加日志消息"""
        self.log_display.insert(tk.END, f"{message}\n")
        self.log_display.see(tk.END)
    
    def clear_chat(self):
        """清空聊天记录"""
        self.chat_display.delete("1.0", tk.END)
        self.chat_history = []

def main():
    """主函数"""
    root = tk.Tk()
    app = ElectromagneticGUI(root)
    root.mainloop()

if __name__ == "__main__":
    main()
