#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import matplotlib.pyplot as plt
import numpy as np
from electromagnetic_agent import ElectromagneticAgent

def main():
    """主程序入口"""
    print("=" * 60)
    print("🔬 电磁场仿真大模型智能体")
    print("=" * 60)
    print("支持的仿真类型：")
    print("1. 电荷分布模拟")
    print("2. 转移电荷模拟") 
    print("3. 输出电流模拟")
    print("=" * 60)
    
    # 初始化智能体
    api_key = "sk-darna2nrdmzrwbcx"  # 您提供的API密钥
    agent = ElectromagneticAgent(api_key)
    
    print("智能体已初始化，请输入您的仿真需求...")
    print("输入 'quit' 或 'exit' 退出程序")
    print("-" * 60)
    
    while True:
        try:
            # 获取用户输入
            user_input = input("\n请描述您的仿真需求: ").strip()
            
            if user_input.lower() in ['quit', 'exit', '退出', 'q']:
                print("感谢使用电磁场仿真智能体！")
                break
            
            if not user_input:
                print("请输入有效的仿真需求")
                continue
            
            print("\n🔄 正在分析您的需求...")
            
            # 处理用户请求
            response = agent.process_request(user_input)
            
            if response['success']:
                print(f"✅ 仿真成功完成！")
                print(f"📊 仿真类型: {response['simulation_type']}")
                print(f"📝 参数说明: {response.get('explanation', '')}")
                
                # 显示使用的参数
                print("\n📋 使用的参数:")
                for key, value in response['parameters'].items():
                    if key != 'f':  # 函数对象不显示
                        print(f"  {key}: {value}")
                
                # 根据仿真类型处理结果
                if response['simulation_type'] == 'charge_distribution':
                    result = response['result']
                    print(f"\n📈 电荷分布计算完成")
                    print(f"   解向量维度: {result.shape}")
                    print(f"   上极板总电荷: {result[:response['parameters']['N_length'] * response['parameters']['N_width']].sum().item() * response['parameters']['b']**2:.6f}")
                    
                elif response['simulation_type'] in ['transfer_charge', 'electric_current']:
                    fig, ax = response['result']
                    print(f"\n📊 图表已生成，正在显示...")
                    plt.show()
                    
                    # 询问是否保存图片
                    save_choice = input("\n是否保存图片？(y/n): ").strip().lower()
                    if save_choice in ['y', 'yes', '是']:
                        filename = f"{response['simulation_type']}_result.png"
                        fig.savefig(filename, dpi=300, bbox_inches='tight')
                        print(f"图片已保存为: {filename}")
                
            else:
                print(f"❌ 仿真失败: {response['error']}")
                print("请检查您的输入并重试")
            
        except KeyboardInterrupt:
            print("\n\n程序被用户中断")
            break
        except Exception as e:
            print(f"❌ 发生错误: {str(e)}")
            print("请重试或联系技术支持")

def demo():
    """演示功能"""
    print("🎯 运行演示示例...")
    
    api_key = "sk-darna2nrdmzrwbcx"
    agent = ElectromagneticAgent(api_key)
    
    # 演示示例
    examples = [
        "计算电荷分布，N_length=50, N_width=50, b=1, rt=1, d=0.6, z=0.6, ebsenr=2.2",
        "分析转移电荷，网格60x60，时间从0到10，步数100，运动方程0.6+0.4*sin(t)",
        "输出电流仿真，参数：长度网格60，宽度网格60，b=1，sigma=1，深度0.6，时间范围0-5"
    ]
    
    for i, example in enumerate(examples, 1):
        print(f"\n--- 示例 {i} ---")
        print(f"输入: {example}")
        
        response = agent.process_request(example)
        
        if response['success']:
            print(f"✅ 成功 - 仿真类型: {response['simulation_type']}")
            if response['simulation_type'] in ['transfer_charge', 'electric_current']:
                plt.show()
        else:
            print(f"❌ 失败: {response['error']}")

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == 'demo':
        demo()
    else:
        main()
