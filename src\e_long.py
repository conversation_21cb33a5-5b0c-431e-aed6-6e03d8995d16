import torch
import numpy as np

def compute_solution_long(N_length, N_width, b, rt, d, z, ebsenr, device=None):
    """
    计算矩形电动力学器件上下极板电荷分布的数值解
    
    参数:
    N_length (int): 长度方向网格数量
    N_width (int): 宽度方向网格数量
    b (float): 基础尺度参数
    rt (float): sigma参数(默认为负值)
    d (float): 深度参数
    z (float): 高度参数
    ebsenr (float): epsilon参数
    device (str, optional): 计算设备 ('cuda' 或 'cpu')，默认自动选择
    
    返回:
    torch.Tensor: x_solution 解向量
    """
    # 检查是否有可用的 GPU
    if device is None:
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    else:
        device = torch.device(device)
    
    q = rt * N_length * N_width
    ebr = torch.tensor(-(ebsenr - 1) / (ebsenr * 2), device=device, dtype=torch.float32)

    # 创建矩形网格
    i, j = np.meshgrid(np.arange(1, N_length+1), np.arange(1, N_width+1), indexing='ij')
    x = (i - 1/2).flatten() * b
    y = (j - 1/2).flatten() * b

    # 将坐标转换为张量并移动到设备
    x = torch.tensor(x, device=device, dtype=torch.float32)
    y = torch.tensor(y, device=device, dtype=torch.float32)

    # 将 d 和 z 转换为张量
    d_tensor = torch.tensor(d, device=device, dtype=torch.float32)
    z_tensor = torch.tensor(z, device=device, dtype=torch.float32)

    # 计算距离矩阵
    dist = torch.sqrt((x[:, None] - x[None, :])**2 + (y[:, None] - y[None, :])**2)
    dist_d = torch.sqrt(dist**2 + d_tensor**2)
    dist_z = torch.sqrt(dist**2 + z_tensor**2)
    dist_dz = torch.sqrt(dist**2 + (d_tensor + z_tensor)**2)

    # 创建矩阵
    lt = torch.where(dist == 0, 4 * b * 0.8814, b**2 / dist)
    ltd = torch.where(dist_d < b, 2 * np.pi * (b / 2) * (-d_tensor / (b / 2) + torch.sqrt((d_tensor / (b / 2))**2 + 4 / np.pi)), b**2 / dist_d)
    ltz = torch.where(dist_z < b, 2 * np.pi * (b / 2) * (-z_tensor / (b / 2) + torch.sqrt((z_tensor / (b / 2))**2 + 4 / np.pi)), b**2 / dist_z)
    ltdz = torch.where(dist_dz < b, 2 * np.pi * (b / 2) * (-(d_tensor + z_tensor) / (b / 2) + torch.sqrt(((d_tensor + z_tensor) / (b / 2))**2 + 4 / np.pi)), b**2 / dist_dz)

    A = ltdz - ebr * (lt - ltd)
    B = lt + ebr * (lt - ltd)
    D = lt + ebr * (ltz - ltdz)
    E = ltdz - ebr * (ltz - ltdz)
    
    # 将矩阵合成为一个矩阵
    H_top = torch.cat((A, B), dim=1)
    H_bottom = torch.cat((D, E), dim=1)
    H = torch.cat((H_top, H_bottom), dim=0)
    
    # 创建一个全为 -1 的列向量
    col_neg_ones = -torch.ones((H.shape[0], 1), device=device, dtype=torch.float32)
    # 创建一个全为 1 的行向量
    row_ones = torch.ones((1, H.shape[1] + 1), device=device, dtype=torch.float32)
    # 将列向量添加到矩阵 H 的最右侧
    H = torch.hstack((H, col_neg_ones))
    # 将行向量添加到矩阵 H 的最底部
    H = torch.vstack((H, row_ones))
    # 将右下角的交界处设置为 0
    H[-1, -1] = 0
    
    
    C = ltd - ebr * (lt - ltd)
    F = ltz + ebr * (ltz - ltdz)
    # 矩形网格的总点数
    total_points = N_length * N_width
    sigmat = torch.full((total_points,), rt, device=device, dtype=torch.float32)
    
    # 计算矩阵 C 和 F 乘以向量 sigmat
    C_sigmat = torch.matmul(C, sigmat)
    F_sigmat = torch.matmul(F, sigmat)
    # 合并结果向量，并在最后添加一个元素
    result_vector = torch.cat((C_sigmat, F_sigmat, torch.tensor([q], device=device, dtype=torch.float32)))
    
    #求解 Hx = result_vector
    x_solution = torch.linalg.solve(H, result_vector)
    # 提取解向量的前 N^2 个元素
    #x_solution_up = x_solution[:N_length * N_width]
    # 提取解向量的中间 N^2 个元素
    #x_solution_down = x_solution[N_length * N_width:2 * N_length * N_width]
    # 提取解向量的最后一个元素
    #voltage = x_solution[-1]
   
    return x_solution


def compute_charge_sum(N_length, N_width, b, rt, d, t, f, ebsenr, device=None):
    """
    计算矩形电动力学器件上下极板电流分布的数值解

    参数:
    N_length (int): 长度方向网格数量
    N_width (int): 宽度方向网格数量
    b (float): 基础尺度参数
    rt (float): sigma参数(默认为负值)
    d (float): 深度参数
    t (float): 时间参数
    f (function): 运动方程函数，f(t)
    ebsenr (float): epsilon参数
    device (str, optional): 计算设备 ('cuda' 或 'cpu')，默认自动选择

    返回:
    torch.Tensor: 电流分布解向量
    """
    
    # 计算电荷分布解向量，f为函数，需传入f(t)
    f_value = f(t)
    x_solution = compute_solution_long(N_length, N_width, b, rt, d, f_value, ebsenr, device=device)
    # 提取解向量的前 N^2 个元素
    x_solution_up = x_solution[:N_length * N_width]
    # 对x_solution_up求和
    sum_x_solution_up = torch.sum(x_solution_up).item() * b**2
    return sum_x_solution_up



