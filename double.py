import torch
import numpy as np
import seaborn as sns
import matplotlib.pyplot as plt
from math import sin,pi

# 检查是否有可用的 GPU
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')

rt = 1.2
d_one = 0.5
d_two = 0.5
l= 200
N=100
b=l/N
z = 1+0.3*sin(2*pi/8000)
ebsenr_one = 2.2
ebsenr_two = 2.2
ebr_one = torch.tensor((ebsenr_one-1)/2/ebsenr_one, device=device, dtype=torch.float32)  # 直接在 GPU 上定义 ebr
ebr_two = torch.tensor((ebsenr_two-1)/2/ebsenr_two, device=device, dtype=torch.float32)

def solve(N, q, b):
    i, j = np.meshgrid(np.arange(1, N+1), np.arange(1, N+1), indexing='ij')
    x = (i - 1/2).flatten() * b
    y = (j - 1/2).flatten() * b
    # 将坐标转换为张量并移动到 GPU
    x = torch.tensor(x, device=device, dtype=torch.float32)
    y = torch.tensor(y, device=device, dtype=torch.float32)
    # 将 d 和 z 转换为张量
    d_one_tensor = torch.tensor(d_one, device=device, dtype=torch.float32)
    d_two_tensor = torch.tensor(d_two, device=device, dtype=torch.float32)
    z_tensor = torch.tensor(z, device=device, dtype=torch.float32)

    # 计算距离矩阵
    dist = torch.sqrt((x[:, None] - x[None, :])**2 + (y[:, None] - y[None, :])**2)
    dist_d_one = torch.sqrt(dist**2 + d_one_tensor**2)
    dist_d_two = torch.sqrt(dist**2 + d_two_tensor**2)
    dist_z = torch.sqrt(dist**2 + z_tensor**2)
    dist_dz_one = torch.sqrt(dist**2 + (d_one_tensor + z_tensor)**2)
    dist_dz_two = torch.sqrt(dist**2 + (d_two_tensor + z_tensor)**2)
    dist_dz = torch.sqrt(dist**2 + (d_one_tensor + d_two_tensor + z_tensor)**2)

    # 创建矩阵
    lt = torch.where(dist == 0, 4 * b * 0.8814, b**2 / dist)
    ltd_one = torch.where(dist_d_one < b, 2 * np.pi * (b / 2) * (-d_one_tensor / (b / 2) + torch.sqrt((d_one_tensor / (b / 2))**2 + 4 / np.pi)), b**2 / dist_d_one)
    ltd_two = torch.where(dist_d_two < b, 2 * np.pi * (b / 2) * (-d_two_tensor / (b / 2) + torch.sqrt((d_two_tensor / (b / 2))**2 + 4 / np.pi)), b**2 / dist_d_two)
    ltz = torch.where(dist_z < b, 2 * np.pi * (b / 2) * (-z_tensor / (b / 2) + torch.sqrt((z_tensor / (b / 2))**2 + 4 / np.pi)), b**2 / dist_z)
    ltdz_one = torch.where(dist_dz_one < b, 2 * np.pi * (b / 2) * (-(d_one_tensor+z_tensor) / (b / 2) + torch.sqrt(((d_one_tensor+z_tensor) / (b / 2))**2 + 4 / np.pi)), b**2 / dist_dz_one)
    ltdz_two = torch.where(dist_dz_two < b, 2 * np.pi * (b / 2) * (-(d_two_tensor+z_tensor) / (b / 2) + torch.sqrt(((d_two_tensor+z_tensor) / (b / 2))**2 + 4 / np.pi)), b**2 / dist_dz_two)
    ltdz = torch.where(dist_dz < b, 2 * np.pi * (b / 2) * (-(d_one_tensor+d_two_tensor+z_tensor) / (b / 2) + torch.sqrt(((d_one_tensor+d_two_tensor+z_tensor) / (b / 2))**2 + 4 / np.pi)), b**2 / dist_dz)

    A = lt-ebr_two*(lt-ltd_two)+ebr_one*(ltdz-ltdz_two)
    B = ltdz-ebr_two*(ltd_two-lt)-ebr_one*(ltdz-ltdz_two)
    D = lt-ebr_one*(lt-ltd_one)+ebr_two*(ltdz-ltdz_one)
    E = ltdz-ebr_one*(ltd_one-lt)-ebr_two*(ltdz-ltdz_one)
    # 将矩阵合成为一个矩阵
    H_top = torch.cat((A, B), dim=1)
    H_bottom = torch.cat((E, D), dim=1)
    H = torch.cat((H_top, H_bottom), dim=0)
    # 创建一个全为 -1 的列向量
    col_neg_ones = -torch.ones((H.shape[0], 1), device=device, dtype=torch.float32)
    # 创建一个全为 1 的行向量
    row_ones = torch.ones((1, H.shape[1] + 1), device=device, dtype=torch.float32)
    # 将列向量添加到矩阵 H 的最右侧
    H = torch.hstack((H, col_neg_ones))
    # 将行向量添加到矩阵 H 的最底部
    H = torch.vstack((H, row_ones))
    # 将右下角的交界处设置为 0
    H[-1, -1] = 0

    sigmat_one = torch.full((N**2,), rt, device=device, dtype=torch.float32)  # 确保 sigmat 已经正确初始化
    sigmat_two = torch.full((N**2,), -rt, device=device, dtype=torch.float32)  # 确保 sigmat 已经正确初始化
    # 计算矩阵 C 和 F 乘以向量 sigmat
    C_sigmat = -torch.matmul(ltdz_two, sigmat_one)-torch.matmul(ltd_two, sigmat_two)
    F_sigmat = -torch.matmul(ltdz_one, sigmat_two)-torch.matmul(ltd_one, sigmat_one)
    # 合并结果向量，并在最后添加一个元素 10000
    result_vector = torch.cat((C_sigmat, F_sigmat, torch.tensor([q], device=device, dtype=torch.float32)))
   
    # 求解 Hx = result_vector
    x_solution = torch.linalg.solve(H, result_vector)
    return x_solution
N=100
b=l/N
q = rt*N**2
x_solution = solve(N, q, b)

# 将张量转换为 NumPy 数组
x_numpy = x_solution.detach().cpu().numpy()
