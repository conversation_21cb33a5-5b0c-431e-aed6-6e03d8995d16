import re
import json
from typing import Dict, Any, Optional, Callable
import numpy as np

class ParameterParser:
    """参数解析器，用于从自然语言中提取仿真参数"""
    
    def __init__(self):
        # 参数模式匹配
        self.param_patterns = {
            'N_length': r'(?:长度方向网格数量|N_length|长度网格)\s*[:=]?\s*(\d+)',
            'N_width': r'(?:宽度方向网格数量|N_width|宽度网格)\s*[:=]?\s*(\d+)',
            'b': r'(?:基础尺度参数|b|尺度参数)\s*[:=]?\s*([\d.]+)',
            'rt': r'(?:sigma参数|rt|σ)\s*[:=]?\s*([-]?[\d.]+)',
            'd': r'(?:深度参数|d|深度)\s*[:=]?\s*([\d.]+)',
            'z': r'(?:高度参数|z|高度)\s*[:=]?\s*([\d.]+)',
            'ebsenr': r'(?:epsilon参数|ebsenr|ε)\s*[:=]?\s*([\d.]+)',
            't_start': r'(?:开始时间|t_start|起始时间)\s*[:=]?\s*([\d.]+)',
            't_end': r'(?:结束时间|t_end|终止时间)\s*[:=]?\s*([\d.]+)',
            'T': r'(?:时间步数|T|步数)\s*[:=]?\s*(\d+)',
            'f': r'(?:运动方程|f|函数)\s*[:=]?\s*(.+?)(?:\n|$)',
        }
        
        # 默认参数值
        self.default_params = {
            'N_length': 60,
            'N_width': 60,
            'b': 1.0,
            'rt': 1.0,
            'd': 0.6,
            'z': 0.6,
            'ebsenr': 2.2,
            't_start': 0.0,
            't_end': 10.0,
            'T': 100,
            'f': lambda t: 0.6 + 0.4 * np.sin(t),
            'save_path': None,
            'device': None
        }
    
    def parse_function(self, func_str: str) -> Callable:
        """解析函数字符串为可调用函数"""
        try:
            # 清理函数字符串
            func_str = func_str.strip()
            
            # 常见函数模式
            if 'sin' in func_str.lower():
                # 提取sin函数参数
                sin_pattern = r'([\d.]+)\s*\+\s*([\d.]+)\s*\*\s*sin\s*\(\s*t\s*\)'
                match = re.search(sin_pattern, func_str)
                if match:
                    a, b = float(match.group(1)), float(match.group(2))
                    return lambda t: a + b * np.sin(t)
            
            elif 'cos' in func_str.lower():
                # 提取cos函数参数
                cos_pattern = r'([\d.]+)\s*\+\s*([\d.]+)\s*\*\s*cos\s*\(\s*t\s*\)'
                match = re.search(cos_pattern, func_str)
                if match:
                    a, b = float(match.group(1)), float(match.group(2))
                    return lambda t: a + b * np.cos(t)
            
            # 尝试直接评估
            if 't' in func_str:
                # 替换常见数学函数
                func_str = func_str.replace('sin', 'np.sin').replace('cos', 'np.cos')
                func_str = func_str.replace('exp', 'np.exp').replace('log', 'np.log')
                return eval(f"lambda t: {func_str}")
            
            # 常数函数
            return lambda t: float(func_str)
            
        except Exception:
            # 返回默认函数
            return self.default_params['f']
    
    def parse_parameters(self, text: str) -> Dict[str, Any]:
        """从文本中解析参数"""
        params = self.default_params.copy()
        
        for param_name, pattern in self.param_patterns.items():
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                value = match.group(1)
                
                if param_name == 'f':
                    params[param_name] = self.parse_function(value)
                elif param_name in ['N_length', 'N_width', 'T']:
                    params[param_name] = int(value)
                else:
                    params[param_name] = float(value)
        
        return params
    
    def detect_simulation_type(self, text: str) -> str:
        """检测仿真类型"""
        text_lower = text.lower()

        # 转移电荷关键词
        transfer_keywords = ['转移电荷', 'transfer charge', '转移', '电荷变化', '电荷转移']
        if any(keyword in text_lower for keyword in transfer_keywords):
            return 'transfer_charge'

        # 电流关键词
        current_keywords = ['电流', 'current', '输出电流', '电流输出', '电流变化']
        if any(keyword in text_lower for keyword in current_keywords):
            return 'electric_current'

        # 电荷分布关键词
        distribution_keywords = ['电荷分布', 'charge distribution', '分布', '电荷计算']
        if any(keyword in text_lower for keyword in distribution_keywords):
            return 'charge_distribution'

        # 时间相关参数存在时，优先判断为时间相关仿真
        if any(keyword in text_lower for keyword in ['时间', 'time', 't_start', 't_end', '步数']):
            if '电流' in text_lower or 'current' in text_lower:
                return 'electric_current'
            else:
                return 'transfer_charge'

        # 默认为电荷分布
        return 'charge_distribution'

    def validate_parameters(self, params: Dict[str, Any], function_type: str) -> Dict[str, Any]:
        """验证参数的有效性"""
        validated = params.copy()

        # 基本验证
        if validated['N_length'] <= 0 or validated['N_width'] <= 0:
            raise ValueError("网格数量必须大于0")

        if validated['b'] <= 0:
            raise ValueError("基础尺度参数必须大于0")

        if validated['d'] <= 0 or validated['z'] <= 0:
            raise ValueError("深度和高度参数必须大于0")

        if validated['ebsenr'] <= 0:
            raise ValueError("epsilon参数必须大于0")

        # 时间相关参数验证（仅对时间相关函数）
        if function_type in ['transfer_charge', 'electric_current']:
            if validated['t_end'] <= validated['t_start']:
                raise ValueError("结束时间必须大于开始时间")

            if validated['T'] <= 0:
                raise ValueError("时间步数必须大于0")

        return validated
