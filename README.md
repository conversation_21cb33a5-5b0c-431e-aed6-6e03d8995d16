# 电磁场仿真大模型智能体

基于DeepSeek-R1大模型的电磁场仿真智能体，支持自然语言交互的电磁场计算和可视化。

## 功能特性

### 🔬 三种仿真模式
1. **电荷分布模拟** - 计算矩形电动力学器件上下极板的电荷分布
2. **转移电荷模拟** - 分析随时间变化的电荷转移情况并生成可视化图表
3. **输出电流模拟** - 计算随时间变化的电流输出并生成可视化图表

### 🤖 智能特性
- 自然语言理解用户需求
- 智能参数提取和验证
- 错误处理和友好提示
- 支持中英文混合输入

## 安装依赖

```bash
pip install -r requirements.txt
```

## 快速开始

### 1. 运行主程序
```bash
python main.py
```

### 2. 运行演示
```bash
python main.py demo
```

### 3. 运行测试
```bash
python test_agent.py
```

## 使用示例

### 电荷分布仿真
```
输入: "计算电荷分布，长度网格60，宽度网格60，b=1，rt=1，d=0.6，z=0.6，epsilon=2.2"
```

### 转移电荷仿真
```
输入: "分析转移电荷变化，网格大小50x50，时间从0到10，步数100，运动方程0.6+0.4*sin(t)"
```

### 电流输出仿真
```
输入: "计算电流输出，N_length=60，N_width=60，时间范围0-5，步数50"
```

## 参数说明

| 参数 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| N_length | 长度方向网格数量 | int | 60 |
| N_width | 宽度方向网格数量 | int | 60 |
| b | 基础尺度参数 | float | 1.0 |
| rt | sigma参数 | float | 1.0 |
| d | 深度参数 | float | 0.6 |
| z | 高度参数 | float | 0.6 |
| ebsenr | epsilon参数 | float | 2.2 |
| t_start | 开始时间 | float | 0.0 |
| t_end | 结束时间 | float | 10.0 |
| T | 时间步数 | int | 100 |
| f | 运动方程函数 | function | λt: 0.6+0.4*sin(t) |

## 支持的运动方程

- 正弦函数: `0.6+0.4*sin(t)`
- 余弦函数: `1.0+0.5*cos(t)`
- 常数函数: `1.5`
- 复合函数: `0.5+0.3*sin(t)+0.1*cos(2*t)`

## 文件结构

```
├── main.py                    # 主程序入口
├── electromagnetic_agent.py   # 智能体核心类
├── api_client.py             # DeepSeek API客户端
├── parameter_parser.py       # 参数解析器
├── test_agent.py            # 测试脚本
├── requirements.txt         # 依赖包列表
├── README.md               # 说明文档
└── src/                    # 仿真核心模块
    ├── e_long.py          # 电荷分布计算
    ├── i_long.py          # 电流和转移电荷计算
    └── ...
```

## API配置

智能体使用DeepSeek-R1大模型，配置信息：
- API URL: `https://cloud.infini-ai.com/maas/v1/chat/completions`
- Model: `deepseek-r1`
- API Key: `sk-darna2nrdmzrwbcx`

## 注意事项

1. 确保所有依赖包已正确安装
2. 网络连接正常以访问DeepSeek API
3. 参数值必须在合理范围内
4. 大网格计算可能需要较长时间

## 故障排除

### 常见问题
1. **API连接失败**: 检查网络连接和API密钥
2. **参数解析错误**: 使用更明确的参数描述
3. **计算超时**: 减小网格大小或时间步数
4. **内存不足**: 降低计算精度或使用GPU加速

### 联系支持
如遇到问题，请检查错误信息并参考文档，或联系技术支持。
