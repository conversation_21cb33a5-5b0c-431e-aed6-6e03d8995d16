import requests
import json
from typing import Dict, Any, Optional

class DeepSeekClient:
    """DeepSeek-R1 API客户端"""
    
    def __init__(self, api_key: str, base_url: str = "https://cloud.infini-ai.com/maas/v1/chat/completions"):
        self.api_key = api_key
        self.base_url = base_url
        self.headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
    
    def chat_completion(self, messages: list, model: str = "deepseek-r1", **kwargs) -> Dict[str, Any]:
        """
        发送聊天完成请求到DeepSeek-R1 API
        
        Args:
            messages: 消息列表
            model: 模型名称
            **kwargs: 其他参数
            
        Returns:
            API响应字典
        """
        payload = {
            "model": model,
            "messages": messages,
            **kwargs
        }
        
        try:
            response = requests.post(
                self.base_url,
                headers=self.headers,
                json=payload,
                timeout=30
            )
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            raise Exception(f"API请求失败: {e}")
    
    def extract_content(self, response: Dict[str, Any]) -> str:
        """从API响应中提取内容"""
        try:
            return response["choices"][0]["message"]["content"]
        except (KeyError, IndexError) as e:
            raise Exception(f"解析API响应失败: {e}")
