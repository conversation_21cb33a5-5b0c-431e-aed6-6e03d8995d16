import torch
import numpy as np
from src.e_long import compute_charge_sum
import matplotlib.pyplot as plt
import seaborn as sns


def compute_electric_current(N_length, N_width, b, rt, d, t_start, t_end, T, f, ebsenr, device=None):
    currents = []
    for t in np.linspace(t_start, t_end, T):
        if t < t_end:
            current = compute_charge_sum(N_length, N_width, b, rt, d, t+(t_end-t_start)/T, f, ebsenr, device=device) - compute_charge_sum(N_length, N_width, b, rt, d, t, f, ebsenr, device=device)
        else:
            current = 0
        currents.append(current / (t_end - t_start) * T)
    return currents  # 返回所有时间点的电流值


def compute_transfer_charge(N_length, N_width, b, rt, d, t_start, t_end, T, f, ebsenr, device=None):
    transfer_charges = []
    for t in np.linspace(t_start, t_end, T):
        transfer_charge = compute_charge_sum(N_length, N_width, b, rt, d, t, f, ebsenr, device=device) - compute_charge_sum(N_length, N_width, b, rt, d, t_start, f, ebsenr, device=device)
        transfer_charges.append(transfer_charge) 
    return transfer_charges  # 返回所有时间点的转移电荷值

def plot_transfer_charges(N_length, N_width, b, rt, d, t_start, t_end, T, f, ebsenr, save_path=None, device=None):
    """
    Visualize the transfer charges over time with an attractive plot using seaborn.
    
    Parameters:
    -----------
    N_length, N_width, b, rt, d, t_start, t_end, T, f, ebsenr, device: 
        Same parameters as in compute_transfer_charge function
    save_path (str, optional): 
        Path to save the figure. If None, the figure will be displayed but not saved.
        
    Returns:
    --------
    fig, ax: matplotlib figure and axis objects
    """
    # Set the seaborn style
    sns.set_theme(style="whitegrid")
    plt.rcParams.update({
        'font.family': 'serif',
        'font.size': 12,
        'axes.labelsize': 14,
        'axes.titlesize': 16,
        'xtick.labelsize': 12,
        'ytick.labelsize': 12
    })
    
    # Compute transfer charges
    transfer_charges = compute_transfer_charge(N_length, N_width, b, rt, d, t_start, t_end, T, f, ebsenr, device)
    print(transfer_charges)
    time_points = np.linspace(t_start, t_end, T)
    
    # Create figure
    fig, ax = plt.subplots(figsize=(10, 6))
    
    # Plot with seaborn
    sns.lineplot(x=time_points, y=transfer_charges, linewidth=2.5, color='#3274A1', ax=ax)
    
    # Add shaded confidence region (for aesthetic purposes)
    ax.fill_between(time_points, 
                   [charge - abs(charge)*0.05 for charge in transfer_charges],
                   [charge + abs(charge)*0.05 for charge in transfer_charges],
                   alpha=0.2, color='#3274A1')
    
    # Add labels and title
    ax.set_xlabel('Time')
    ax.set_ylabel('Transfer Charge')
    ax.set_title('Transfer Charge vs. Time')
    
    # Add grid and spines
    ax.grid(True, linestyle='--', alpha=0.7)
    for spine in ax.spines.values():
        spine.set_linewidth(1.5)
    
    # Add parameter information as text
    param_text = f"Parameters: b={b}, σ={rt}, d={d}, ε={ebsenr}"
    plt.figtext(0.5, 0.01, param_text, ha="center", fontsize=12, 
                bbox={"facecolor":"#f0f0f0", "alpha":0.5, "pad":5})
    
    plt.tight_layout(rect=[0, 0.03, 1, 0.97])
    
    # Save figure if path is provided
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
    
    return fig, ax

def plot_electric_currents(N_length, N_width, b, rt, d, t_start, t_end, T, f, ebsenr, save_path=None, device=None):
    """
    Visualize the electric currents over time with an attractive plot using seaborn.
    
    Parameters:
    -----------
    N_length, N_width, b, rt, d, t_start, t_end, T, f, ebsenr, device: 
        Same parameters as in compute_electric_current function
    save_path (str, optional): 
        Path to save the figure. If None, the figure will be displayed but not saved.
        
    Returns:
    --------
    fig, ax: matplotlib figure and axis objects
    """
    # Set the seaborn style
    sns.set_theme(style="whitegrid")
    plt.rcParams.update({
        'font.family': 'serif',
        'font.size': 12,
        'axes.labelsize': 14,
        'axes.titlesize': 16,
        'xtick.labelsize': 12,
        'ytick.labelsize': 12
    })
    
    # Compute electric currents
    currents = compute_electric_current(N_length, N_width, b, rt, d, t_start, t_end, T, f, ebsenr, device)
    print(currents)
    time_points = np.linspace(t_start, t_end, T)
    
    # Create figure
    fig, ax = plt.subplots(figsize=(10, 6))
    
    # Plot with seaborn
    sns.lineplot(x=time_points, y=currents, linewidth=2.5, color='#E6550D', ax=ax)
    
    # Add shaded confidence region (for aesthetic purposes)
    ax.fill_between(time_points, 
                   [current - abs(current)*0.05 for current in currents],
                   [current + abs(current)*0.05 for current in currents],
                   alpha=0.2, color='#E6550D')
    
    # Add labels and title
    ax.set_xlabel('Time')
    ax.set_ylabel('Electric Current')
    ax.set_title('Electric Current vs. Time')
    
    # Add grid and spines
    ax.grid(True, linestyle='--', alpha=0.7)
    for spine in ax.spines.values():
        spine.set_linewidth(1.5)
    
    # Add parameter information as text
    param_text = f"Parameters: b={b}, σ={rt}, d={d}, ε={ebsenr}"
    plt.figtext(0.5, 0.01, param_text, ha="center", fontsize=12, 
                bbox={"facecolor":"#f0f0f0", "alpha":0.5, "pad":5})
    
    plt.tight_layout(rect=[0, 0.03, 1, 0.97])
    
    # Save figure if path is provided
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
    
    return fig, ax