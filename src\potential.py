import torch
import numpy as np
from src.e_long import compute_solution_long

def compute_voltage(N_length, N_width, b, rt, d, z, ebsenr, device=None):
    # 宏定义常数con
    con = 1/(4*np.pi*8.854187817*10**(-12))
    ebr = torch.tensor(-(ebsenr - 1) / (ebsenr * 2), device=device, dtype=torch.float32)
    # 计算 x_solution
    x_solution = compute_solution_long(N_length, N_width, b, rt, d, z, ebsenr)
    # 计算上极板电荷分布
    x_solution_up = x_solution[:N_length * N_width]
    # 计算下极板电荷分布
    x_solution_down = x_solution[N_length * N_width:2 * N_length * N_width]
    # 将rt装换成与x_solution_up维度相同的张量
    rt = torch.full_like(x_solution_up, rt)
    # 计算极化电荷分布
    x_solution_polarized = ebr*(x_solution_up - x_solution_down-rt)
    # 计算上极板在坐标(m, n, h)处的电势
    def potential_up(m, n, h):

        potential = 0
        
        for i in range(N_length):
            for j in range(N_width):
                # 计算两点之间的距离
                r = np.sqrt((m - i) ** 2 + (n - j) ** 2 + (int((d + z) / b)-h) ** 2)
                # 计算势能
                potential += x_solution_up[i * N_width + j] / r
        # 返回势能乘以常数
        return potential * con

    # 计算下极板在坐标(m, n, h)处的电势
    def potential_down(m, n, h):
        potential = 0
        for i in range(N_length):
            for j in range(N_width):
                r = np.sqrt((m - i ) ** 2 + (n - j ) ** 2 + h ** 2)
                potential += (x_solution_down[i * N_width + j] - x_solution_polarized[i * N_width + j]) / r
        return potential * con

    # 计算介质层在坐标(m, n, h)处的电势
    def potential_medium(m, n, h):
        potential = 0
        for i in range(N_length):
            for j in range(N_width):
                r = np.sqrt((m - i ) ** 2 + (n - j ) ** 2 + (int(d/b)-h) ** 2)
                potential += (x_solution_polarized[i * N_width + j]+rt[i * N_width + j]) / r
        return potential * con
        
    # 计算总电势
    def potential_total(m, n, h):
        return potential_up(m, n, h) + potential_down(m, n, h) + potential_medium(m, n, h)

    # 计算总电势分布
    def compute_total_potential_distribution():
        potential_distribution = np.zeros((N_length, N_width, int((d + z) / b)))
        for h in range(int((d + z) / b)):
            for m in range(N_length):
                for n in range(N_width):
                    potential_distribution[m, n, h] = potential_total(m, n, h+0.5)
        return potential_distribution*b
    
    # 计算电势分布
    potential_distribution = compute_total_potential_distribution()
    return potential_distribution


# 计算电场分布
def compute_electric_field(N_length, N_width, b, rt, d, z, ebsenr, device=None):
    # 根据电势分布计算电场分布
    potential_distribution = compute_voltage(N_length, N_width, b, rt, d, z, ebsenr, device)
    # 计算电场分布
    
    # 初始化电场分布数组 (Ex, Ey, Ez)
    Ex = np.zeros_like(potential_distribution)
    Ey = np.zeros_like(potential_distribution)
    Ez = np.zeros_like(potential_distribution)
    
    # 计算x方向电场分量 Ex = -dV/dx
    for i in range(N_length):
        for j in range(N_width):
            for k in range(int((d + z) / b)):
                if i == 0:
                    # 前向差分
                    Ex[i, j, k] = -(potential_distribution[i+1, j, k] - potential_distribution[i, j, k]) / b
                elif i == N_length - 1:
                    # 后向差分
                    Ex[i, j, k] = -(potential_distribution[i, j, k] - potential_distribution[i-1, j, k]) / b
                else:
                    # 中心差分
                    Ex[i, j, k] = -(potential_distribution[i+1, j, k] - potential_distribution[i-1, j, k]) / (2 * b)
    
    # 计算y方向电场分量 Ey = -dV/dy
    for i in range(N_length):
        for j in range(N_width):
            for k in range(int((d + z) / b)):
                if j == 0:
                    # 前向差分
                    Ey[i, j, k] = -(potential_distribution[i, j+1, k] - potential_distribution[i, j, k]) / b
                elif j == N_width - 1:
                    # 后向差分
                    Ey[i, j, k] = -(potential_distribution[i, j, k] - potential_distribution[i, j-1, k]) / b
                else:
                    # 中心差分
                    Ey[i, j, k] = -(potential_distribution[i, j+1, k] - potential_distribution[i, j-1, k]) / (2 * b)
    
    # 计算z方向电场分量 Ez = -dV/dz
    for i in range(N_length):
        for j in range(N_width):
            for k in range(int((d + z) / b)):
                if k == 0:
                    # 前向差分
                    Ez[i, j, k] = -(potential_distribution[i, j, k+1] - potential_distribution[i, j, k]) / b
                elif k == int((d + z) / b) - 1:
                    # 后向差分
                    Ez[i, j, k] = -(potential_distribution[i, j, k] - potential_distribution[i, j, k-1]) / b
                else:
                    # 中心差分
                    Ez[i, j, k] = -(potential_distribution[i, j, k+1] - potential_distribution[i, j, k-1]) / (2 * b)
    
    # 返回电场分布的三个分量
    return Ex, Ey, Ez

# 计算电场强度分布
def compute_electric_field_magnitude(N_length, N_width, b, rt, d, z, ebsenr, device=None):
    Ex, Ey, Ez = compute_electric_field(N_length, N_width, b, rt, d, z, ebsenr, device)
    # 计算电场强度 |E| = sqrt(Ex^2 + Ey^2 + Ez^2)
    E_magnitude = np.sqrt(Ex**2 + Ey**2 + Ez**2)
    return E_magnitude
    